#!/usr/bin/env node

/**
 * Script de configuración automática para variables de entorno
 * Ayuda a los desarrolladores a configurar el archivo .env
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

console.log('🚀 Botsito Backend - Setup de Variables de Entorno\n');

// Verificar si .env ya existe
const envPath = path.join(projectRoot, '.env');
const envExamplePath = path.join(projectRoot, '.env.example');

if (fs.existsSync(envPath)) {
  console.log('⚠️  El archivo .env ya existe.');
  console.log('   Si quieres recrearlo, elimínalo primero y ejecuta este script nuevamente.\n');
  
  // Verificar si las variables están configuradas
  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'FIREBASE_PROJECT_ID',
    'FIREBASE_CLIENT_EMAIL', 
    'FIREBASE_PRIVATE_KEY',
    'COLLECTION',
    'DOC_ID'
  ];
  
  const missingVars = requiredVars.filter(varName => 
    !envContent.includes(`${varName}=`) || 
    envContent.includes(`${varName}=tu-`) ||
    envContent.includes(`${varName}=TU_`)
  );
  
  if (missingVars.length > 0) {
    console.log('❌ Variables de entorno faltantes o sin configurar:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n📝 Edita el archivo .env y configura estas variables.');
  } else {
    console.log('✅ Todas las variables requeridas están configuradas.');
  }
  
  process.exit(0);
}

// Verificar si .env.example existe
if (!fs.existsSync(envExamplePath)) {
  console.error('❌ No se encontró el archivo .env.example');
  console.error('   Este archivo debería estar en la raíz del proyecto.');
  process.exit(1);
}

// Copiar .env.example a .env
try {
  fs.copyFileSync(envExamplePath, envPath);
  console.log('✅ Archivo .env creado desde .env.example');
  console.log('📝 Ahora necesitas configurar las variables de entorno:\n');
  
  console.log('🔧 PASOS SIGUIENTES:');
  console.log('1. Abre el archivo .env en tu editor');
  console.log('2. Configura las variables de Firebase:');
  console.log('   - FIREBASE_PROJECT_ID');
  console.log('   - FIREBASE_CLIENT_EMAIL');
  console.log('   - FIREBASE_PRIVATE_KEY');
  console.log('3. Configura las variables de Firestore:');
  console.log('   - COLLECTION');
  console.log('   - DOC_ID');
  console.log('4. (Opcional) Configura las variables de Zoho Calendar');
  console.log('5. Ejecuta: npm run build && npm start');
  console.log('\n📚 Para más detalles, revisa los comentarios en .env.example');
  
} catch (error) {
  console.error('❌ Error creando el archivo .env:', error.message);
  process.exit(1);
}
