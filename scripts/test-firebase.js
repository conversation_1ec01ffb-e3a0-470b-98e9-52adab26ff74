#!/usr/bin/env node

/**
 * Test completo de conectividad Firebase
 * Verifica que todas las funcionalidades Firebase funcionen correctamente
 */

import dotenv from 'dotenv';
import admin from 'firebase-admin';

// Cargar variables de entorno
dotenv.config();

// Validación simple de variables Firebase
function validateFirebaseEnv() {
  const requiredVars = [
    'FIREBASE_PROJECT_ID',
    'FIREBASE_CLIENT_EMAIL',
    'FIREBASE_PRIVATE_KEY',
  ];
  const missing = requiredVars.filter((varName) => !process.env[varName]);

  if (missing.length > 0) {
    throw new Error(`Variables de entorno faltantes: ${missing.join(', ')}`);
  }

  return {
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
    FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,
  };
}

console.log('🔥 BOTSITO - Test de Conectividad Firebase\n');

let testsPassed = 0;
let testsTotal = 0;

function runTest(testName, testFn) {
  testsTotal++;
  console.log(`🧪 Test ${testsTotal}: ${testName}`);

  try {
    const result = testFn();
    if (result instanceof Promise) {
      return result
        .then(() => {
          testsPassed++;
          console.log(`   ✅ PASSED\n`);
        })
        .catch((error) => {
          console.log(`   ❌ FAILED: ${error.message}\n`);
        });
    } else {
      testsPassed++;
      console.log(`   ✅ PASSED\n`);
    }
  } catch (error) {
    console.log(`   ❌ FAILED: ${error.message}\n`);
  }
}

async function runAllTests() {
  // Test 1: Validación de variables de entorno
  await runTest('Validación de variables de entorno', () => {
    const firebaseEnv = validateFirebaseEnv();
    if (!firebaseEnv.FIREBASE_PROJECT_ID)
      throw new Error('FIREBASE_PROJECT_ID no válido');
    if (!firebaseEnv.FIREBASE_CLIENT_EMAIL)
      throw new Error('FIREBASE_CLIENT_EMAIL no válido');
    if (!firebaseEnv.FIREBASE_PRIVATE_KEY)
      throw new Error('FIREBASE_PRIVATE_KEY no válido');
    console.log(`     📊 Proyecto: ${firebaseEnv.FIREBASE_PROJECT_ID}`);
    console.log(`     📧 Email: ${firebaseEnv.FIREBASE_CLIENT_EMAIL}`);
  });

  // Test 2: Inicialización de Firebase Admin
  await runTest('Inicialización de Firebase Admin', () => {
    if (admin.apps.length === 0) {
      const firebaseEnv = validateFirebaseEnv();
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: firebaseEnv.FIREBASE_PROJECT_ID,
          clientEmail: firebaseEnv.FIREBASE_CLIENT_EMAIL,
          privateKey: firebaseEnv.FIREBASE_PRIVATE_KEY,
        }),
      });
    }
    console.log(`     🔥 Apps inicializadas: ${admin.apps.length}`);
  });

  // Test 3: Conexión a Firestore
  await runTest('Conexión a Firestore', async () => {
    const db = admin.firestore();

    // Test de conectividad básica
    const testCollection = db.collection('botsito_test');
    const testDoc = testCollection.doc('connectivity-test');

    // Intentar escribir un documento de prueba
    await testDoc.set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      test: 'connectivity',
      source: 'botsito-backend-test',
    });

    console.log(`     📝 Documento de prueba creado`);

    // Leer el documento
    const doc = await testDoc.get();
    if (!doc.exists) {
      throw new Error('No se pudo leer el documento de prueba');
    }

    console.log(`     📖 Documento de prueba leído`);

    // Limpiar - eliminar el documento de prueba
    await testDoc.delete();
    console.log(`     🗑️  Documento de prueba eliminado`);
  });

  // Test 4: Verificar colección principal (si está configurada)
  if (process.env.COLLECTION && process.env.DOC_ID) {
    await runTest('Acceso a colección principal', async () => {
      const db = admin.firestore();
      const mainDoc = db
        .collection(process.env.COLLECTION)
        .doc(process.env.DOC_ID);

      const doc = await mainDoc.get();
      if (doc.exists) {
        console.log(`     📂 Documento principal encontrado`);
        console.log(
          `     🗂️  Colección: ${process.env.COLLECTION}/${process.env.DOC_ID}`
        );

        // Verificar si tiene subcolecciones comunes
        const subcollections = ['appointments', 'clients', 'users'];
        for (const subcol of subcollections) {
          try {
            const snapshot = await mainDoc.collection(subcol).limit(1).get();
            console.log(
              `     📁 Subcolección '${subcol}': ${snapshot.size} documentos (muestra)`
            );
          } catch (error) {
            console.log(`     ⚠️  Subcolección '${subcol}': No accesible`);
          }
        }
      } else {
        console.log(
          `     ⚠️  Documento principal no existe (puede ser normal en desarrollo)`
        );
      }
    });
  }

  // Test 5: Verificar permisos de escritura
  await runTest('Permisos de escritura en Firestore', async () => {
    const db = admin.firestore();
    const testCollection = db.collection('botsito_permissions_test');
    const testDoc = testCollection.doc('write-test');

    // Intentar operaciones CRUD
    await testDoc.set({ test: 'create', timestamp: new Date() });
    console.log(`     ✏️  CREATE: OK`);

    await testDoc.update({ test: 'update', updated: new Date() });
    console.log(`     📝 UPDATE: OK`);

    const doc = await testDoc.get();
    if (!doc.exists || doc.data().test !== 'update') {
      throw new Error('READ después de UPDATE falló');
    }
    console.log(`     📖 READ: OK`);

    await testDoc.delete();
    console.log(`     🗑️  DELETE: OK`);
  });

  // Resumen final
  console.log('='.repeat(50));
  console.log(`📊 RESUMEN DE TESTS:`);
  console.log(`   Tests ejecutados: ${testsTotal}`);
  console.log(`   Tests exitosos: ${testsPassed}`);
  console.log(`   Tests fallidos: ${testsTotal - testsPassed}`);

  if (testsPassed === testsTotal) {
    console.log(
      `\n🎉 ¡Todos los tests pasaron! Firebase está configurado correctamente.`
    );
    process.exit(0);
  } else {
    console.log(
      `\n❌ Algunos tests fallaron. Revisa la configuración de Firebase.`
    );
    process.exit(1);
  }
}

// Ejecutar todos los tests
runAllTests().catch((error) => {
  console.error('💥 Error ejecutando tests:', error.message);
  process.exit(1);
});
