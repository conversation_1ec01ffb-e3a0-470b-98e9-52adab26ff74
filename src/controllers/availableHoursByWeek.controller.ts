// availableWeekHoursController.ts
import QueryString from 'qs';
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import { TimeRange } from '~/model/CompanyModel';
import {
  getFilteredAvailableSlotsForDay,
  extractHours,
} from '~/utils/functions';

interface WeekAvailableHoursRequestBody {
  /**
   * Fecha de inicio de la semana en formato 'YYYY-MM-DD'
   */
  date: string;
  /**
   * Objeto workDays de companyData, enviado como JSON.
   */
  workDays: string;
  /**
   * Array de días off, enviado como JSON.
   */
  daysOff: string;
}

interface WeekAvailableHoursResponse {
  weekAvailableHours: Record<string, string[]>;
}

/**
 * Controlador para obtener las horas disponibles de la semana (7 días).
 * Se espera que se reciba en la query:
 * - date: string (fecha de inicio en formato 'YYYY-MM-DD')
 * - workDays: JSON string con la información de workDays.
 * - daysOff: JSON string con el array de días off.
 */
export const availableWeekHoursController = async (
  bot: BotType,
  req: RequestType<{}, WeekAvailableHoursRequestBody>,
  res: ResponseType
) => {
  try {
    const parsedQuery = QueryString.parse(req.url?.split('?')[1] || '');
    const { date, workDays, daysOff } = parsedQuery as {
      date: string;
      workDays: string;
      daysOff: string;
    };

    if (!date || !workDays || !daysOff) {
      const errorResponse: ErrorResponse = {
        message:
          'Parámetros inválidos. Se requieren "date", "workDays" y "daysOff".',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Parsear los strings JSON
    let parsedWorkDays: Record<
      string,
      { isAvailable: boolean; workTime: TimeRange[]; dayName: string }
    >;
    let parsedDaysOff: string[];

    try {
      parsedWorkDays = JSON.parse(workDays);
      parsedDaysOff = JSON.parse(daysOff);
    } catch (err) {
      const errorResponse: ErrorResponse = {
        message: 'Error al parsear workDays o daysOff.',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Calcular el rango de 7 días a partir de la fecha de inicio
    const startDate = new Date(`${date}T00:00:00`);
    const weekAvailableHours: Record<string, string[]> = {};

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const dayString = `${year}-${month}-${day}`;

      // Verificar si la fecha está en daysOff (comparando solo la parte de fecha)
      const isDayOff = parsedDaysOff.some((off) => {
        const offDate = new Date(off);
        return offDate.toISOString().slice(0, 10) === dayString;
      });
      if (isDayOff) {
        weekAvailableHours[dayString] = [];
        continue;
      }

      const jsDay = currentDate.getDay();
      const key = jsDay.toString();
      const workDayData = parsedWorkDays[key];
      if (!workDayData || workDayData.isAvailable === false) {
        weekAvailableHours[dayString] = [];
        continue;
      }

      // Obtener los slots disponibles usando los workTime del día
      const slots = await getFilteredAvailableSlotsForDay(
        workDayData.workTime,
        dayString
      );
      const formattedHours = slots ? extractHours(slots) : [];
      weekAvailableHours[dayString] = formattedHours;
    }

    const response: SuccessResponse<WeekAvailableHoursResponse> = {
      statusCode: ReponseStatusCode.Success,
      data: { weekAvailableHours },
    };

    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } catch (error) {
    console.error(
      'Error al obtener las horas disponibles para la semana:',
      error
    );
    const errorResponse: ErrorResponse = {
      message:
        'Error interno del servidor al obtener las horas disponibles para la semana',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
