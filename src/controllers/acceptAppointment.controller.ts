import firebaseServiceInstance from '~/database/firebaseService';
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import { acceptFirestoreAppointment } from '~/services/appointmentService';

interface RequestBody {
  phoneNumber: string;
  message: string;
  tokenCode?: string;
  tokenExpiresAt?: number;
}

interface AcceptResponse {
  hasAccepted: boolean;
}

export const acceptAppointmentController = async (
  bot: BotType,
  req: RequestType<RequestBody>,
  res: ResponseType
) => {
  try {
    const { phoneNumber, message, tokenCode, tokenExpiresAt } = req.body;
    console.log(phoneNumber, message);

    const acceptResult = await acceptFirestoreAppointment(phoneNumber);

    // Esto deberia de ir aqui? o en el if?
    if (tokenCode && Date.now() < tokenExpiresAt) {
      await firebaseServiceInstance.markAppointmentTokenUsed(tokenCode);
    }

    const response: SuccessResponse<AcceptResponse> = {
      statusCode: ReponseStatusCode.ServerError,
      data: {
        hasAccepted: false,
      },
    };

    if (acceptResult) {
      response.statusCode = ReponseStatusCode.Success;
      response.data.hasAccepted = true;

      await bot.sendMessage(phoneNumber, message, {});

      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(response));
    } else {
      res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(response));
    }
  } catch (error) {
    console.error('Error al aceptar la cita:', error);

    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor al aceptar la cita',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
