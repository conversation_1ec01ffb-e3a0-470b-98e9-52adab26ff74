import { BotType, RequestType, ResponseType } from '~/model/ApiModel';
import { BlackListIntent } from '~/model/CommonModel';

interface RequestBody {
  number: string;
  intent: BlackListIntent;
}

export const blackListController = async (
  bot: BotType,
  req: RequestType<RequestBody>,
  res: ResponseType
) => {
  try {
    const { number, intent } = req.body;
    if (intent === 'remove') bot.blacklist.remove(number);
    if (intent === 'add') bot.blacklist.add(number);

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'ok', number, intent }));
  } catch (error) {
    console.error('Error sending message:', error);
    res.statusCode = 500;
    res.end('Error sending message');
  }
};
