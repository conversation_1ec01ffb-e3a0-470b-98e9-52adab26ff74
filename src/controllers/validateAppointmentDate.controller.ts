import QueryString from 'qs';
import moment from 'moment-timezone';
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import zohoInstance from '~/calendar/ClassZohoApi';

interface ValidateAppointmentRequestBody {
  /**
   * Fecha en formato ISO (por ejemplo: "2025-02-20T06:00:00.000Z")
   */
  date: string;
  /**
   * Hora en formato "h:mma" (por ejemplo: "10:00am")
   */
  time: string;
}

interface ValidateAppointmentResponse {
  isAvailable: boolean;
}

/**
 * Endpoint para validar si un slot (fecha y hora) está disponible.
 *
 * Se espera recibir en la query string (o en el body) un objeto con:
 * {
 *    "date": "2025-02-20T06:00:00.000Z",
 *    "time": "10:00am"
 * }
 *
 * La función consulta Zoho Calendar y retorna:
 * {
 *    "isAvailable": true  // si el slot está disponible
 * }
 * o
 * {
 *    "isAvailable": false // si ya se agendó una cita en ese slot
 * }
 */
export const validateAppointmentDateController = async (
  bot: BotType,
  req: RequestType<{}, {}>,
  res: ResponseType
): Promise<void> => {
  try {
    // Extrae la parte de la query string de la URL y la convierte en un objeto.
    // Si no existe query string, se utiliza una cadena vacía.
    const parsedQuery = QueryString.parse(req.url?.split('?')[1] || '');
    const { date, time } =
      parsedQuery as unknown as ValidateAppointmentRequestBody;

    console.log('Validar cita - URL:', req.url);
    console.log('Parsed query:', parsedQuery);

    if (!date || !time) {
      const errorResponse: ErrorResponse = {
        message: 'Parámetros inválidos. Se requiere "date" y "time".',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Definir zona horaria (la misma que usa Zoho)
    const timezone = 'America/Mexico_City';

    // Parsear la fecha y la hora
    const dateMoment = moment.tz(date, timezone);
    const timeMoment = moment.tz(time, 'h:mma', timezone);

    // Combinar la fecha y la hora para obtener el inicio del slot
    const slotStart = dateMoment.clone().set({
      hour: timeMoment.hour(),
      minute: timeMoment.minute(),
      second: 0,
      millisecond: 0,
    });
    // Se asume que la duración del slot es de 1 hora
    const slotEnd = slotStart.clone().add(1, 'hour');

    // Obtener el día en formato YYYY-MM-DD para consultar las citas de ese día
    const dateString = slotStart.format('YYYY-MM-DD');

    // ? Podria usar este metodo ya existente
    // const isSlotAvailable = await zohoInstance.isSlotAvailable(
    //   slotStart,
    //   slotEnd
    // );

    // Consultar en Zoho todas las citas del día (se puede ajustar el rango si se prefiere)
    const bookedAppointments = await zohoInstance.getAppointments(
      dateString,
      0,
      24
    );

    // Verificar si alguna cita se solapa con nuestro slot
    const isBooked = bookedAppointments.some((appointment) => {
      const appStart = moment(appointment.start);
      const appEnd = moment(appointment.end);
      return (
        // Comprueba si el inicio o el fin del slot cae dentro de una cita existente
        slotStart.isBetween(appStart, appEnd, undefined, '[)') ||
        slotEnd.isBetween(appStart, appEnd, undefined, '(]')
      );
    });

    const response: SuccessResponse<ValidateAppointmentResponse> = {
      statusCode: ReponseStatusCode.Success,
      data: { isAvailable: !isBooked },
    };

    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } catch (error) {
    console.error('Error al validar el slot de la cita:', error);
    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor al validar el slot de la cita',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
