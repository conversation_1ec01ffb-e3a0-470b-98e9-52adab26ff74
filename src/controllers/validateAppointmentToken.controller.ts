// src/controllers/validateAppointmentToken.controller.ts
import {
  RequestType,
  ResponseType,
  HttpStatus,
  ReponseStatusCode,
  ErrorResponse,
  BotType,
  SuccessResponse,
} from '~/model/ApiModel';
import firebaseServiceInstance from '~/database/firebaseService';
import jwt from 'jsonwebtoken';
import {
  TokenValidationCode,
  TokenValidationMessages,
  TokenValidationRequestQuery,
  TokenValidationResponse,
} from '~/model/AppointmentTokenModel';

/**
 * Controlador para validar el token (a través del código corto)
 * y obtener los detalles de la cita almacenados en el JWT.
 */
export const validateAppointmentTokenController = async (
  bot: BotType,
  req: RequestType<{}, TokenValidationRequestQuery>,
  res: ResponseType
) => {
  try {
    const { token } = req.query;
    if (!token) {
      // Falta el parámetro 'code': este es un error real.
      const errorResponse: ErrorResponse = {
        message: TokenValidationMessages.MISSING_CODE,
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Recuperar el documento del token en Firestore usando el código corto
    const tokenDoc = await firebaseServiceInstance.getAppointmentToken(
      token.toString()
    );
    if (!tokenDoc) {
      // No se encontró el token: consideramos que es un caso de token inválido.
      const responsePayload: SuccessResponse<TokenValidationResponse> = {
        statusCode: ReponseStatusCode.Success,
        data: {
          valid: false,
          tokenCode: TokenValidationCode.TOKEN_INVALID,
          message: TokenValidationMessages.TOKEN_INVALID,
        },
      };
      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(responsePayload));
      return;
    }

    // Validar si el token ya fue utilizado
    if (tokenDoc.used) {
      const responsePayload: SuccessResponse<TokenValidationResponse> = {
        statusCode: ReponseStatusCode.Success,
        data: {
          valid: false,
          tokenCode: TokenValidationCode.TOKEN_ALREADY_USED,
          message: TokenValidationMessages.TOKEN_ALREADY_USED,
        },
      };
      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(responsePayload));
      return;
    }

    // Validar expiración (suponiendo que tokenDoc.expiresAt es un timestamp en milisegundos)
    if (tokenDoc.expiresAt && Date.now() > tokenDoc.expiresAt) {
      const responsePayload: SuccessResponse<TokenValidationResponse> = {
        statusCode: ReponseStatusCode.Success,
        data: {
          valid: false,
          tokenCode: TokenValidationCode.TOKEN_EXPIRED,
          message: TokenValidationMessages.TOKEN_EXPIRED,
        },
      };
      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(responsePayload));
      return;
    }

    // Verificar el JWT almacenado en tokenDoc.token
    const secret = process.env.JWT_SECRET || 'TU_SECRETO_MUY_SEGURO';
    let decoded: any; // TODO: add types
    try {
      decoded = jwt.verify(tokenDoc.token, secret);
    } catch (error) {
      const responsePayload: SuccessResponse<TokenValidationResponse> = {
        statusCode: ReponseStatusCode.Success,
        data: {
          valid: false,
          tokenCode: TokenValidationCode.TOKEN_INVALID,
          message: TokenValidationMessages.TOKEN_INVALID,
        },
      };
      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(responsePayload));
      return;
    }

    // Construir el objeto con los detalles que se enviarán al front
    const appointmentDetails = {
      appointmentId: decoded.appointmentId,
      phoneNumber: decoded.phoneNumber,
      newStartDate: decoded.newStartDate,
      newTime: decoded.newTime,
      eventUid: decoded.eventUid,
      etag: decoded.etag,
    };

    const responsePayload: SuccessResponse<TokenValidationResponse> = {
      statusCode: ReponseStatusCode.Success,
      data: {
        valid: true,
        message: 'Token válido.',
        tokenCode: TokenValidationCode.VALID,
        appointmentData: appointmentDetails,
      },
    };

    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(responsePayload));
  } catch (error) {
    console.error('Error validating appointment token:', error);
    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor.',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
