import { BotType, RequestType, ResponseType } from '~/model/ApiModel';

interface RequestBody {
  number: string;
  message: string;
}

export const sendMessageController = async (
  bot: BotType,
  req: RequestType<RequestBody>,
  res: ResponseType
) => {
  try {
    const { number, message } = req.body;
    console.log(req.body);

    await bot.sendMessage(number, message, {});

    res.statusCode = 200;
    res.end('Message sent');
  } catch (error) {
    console.error('Error sending message:', error);
    res.statusCode = 500;
    res.end('Error sending message');
  }
};
