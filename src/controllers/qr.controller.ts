import { join } from 'path';
import fs from 'fs';

export const qrController = async (bot: any, req: any, res: any) => {
  try {
    const PATH_QR = join(process.cwd(), 'bot.qr.png');

    if (!fs.existsSync(PATH_QR)) {
      res.writeHead(404);
      return res.end('No se encontró el archivo QR');
    }

    const fileStream = fs.createReadStream(PATH_QR);
    res.writeHead(200, { 'Content-Type': 'image/png' });
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error leyendo el QR:', error);
    res.writeHead(500);
    res.end('Ocurrió un error al leer el archivo QR');
  }
};
