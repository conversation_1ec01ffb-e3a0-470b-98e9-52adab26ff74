import {
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import { deleteFirestoreAppointment } from '~/services/appointmentService';

interface RequestBody {
  phoneNumber: string;
}

interface DeleteResponse {
  hasDeleted: boolean;
}

export const deleteAppointmentController = async (
  _: any,
  req: RequestType<RequestBody>,
  res: ResponseType
) => {
  try {
    const { phoneNumber: number } = req.body;
    console.log(number);

    const deletionResult = await deleteFirestoreAppointment(number);

    const response: SuccessResponse<DeleteResponse> = {
      statusCode: ReponseStatusCode.ServerError,
      data: {
        hasDeleted: false,
      },
    };

    if (deletionResult) {
      response.statusCode = ReponseStatusCode.Success;
      response.data.hasDeleted = true;

      res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(response));
    } else {
      res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(response));
    }
  } catch (error) {
    console.error('Error deleting appointment:', error);

    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor al intentar eliminar la cita',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
