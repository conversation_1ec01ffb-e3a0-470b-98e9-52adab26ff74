// controllers/eventsController.ts
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import zohoInstance from '~/calendar/ClassZohoApi';
import { EventDetails } from '~/model/ZohoCalendarModel';

// Interfaz para los parámetros de query
interface GetEventsQueryParams {
  start: string;
  end: string;
  view?: string;
}

// Interfaz para la respuesta de eventos
interface GetEventsResponseData {
  events: EventDetails[];
}

export const getEventsController = async (
  bot: BotType,
  req: RequestType<{}, GetEventsQueryParams>,
  res: ResponseType
) => {
  try {
    console.log('🔍 getEventsController iniciado');

    // Se castea req.query a la interfaz GetEventsQueryParams
    const { start, end, view } = req.query;
    console.log('📋 Parámetros recibidos:', { start, end, view });

    if (!start || !end) {
      console.log('❌ Parámetros faltantes');
      const errorResponse: ErrorResponse = {
        message: 'Parámetros "start" y "end" son requeridos',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Convertir los parámetros a Date
    const startDate = new Date(start);
    const endDate = new Date(end);
    console.log('📅 Fechas convertidas:', { startDate, endDate });

    // Verificar que las fechas sean válidas
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.log('❌ Fechas inválidas');
      const errorResponse: ErrorResponse = {
        message: 'Fechas inválidas. Use formato ISO 8601',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    console.log('📤 Llamando a zohoInstance.getEventsByRange...');

    // Se llama al método que obtiene solo los eventos dentro del rango
    // Esto evita traer todos los eventos y luego filtrarlos
    const eventsData: EventDetails[] = await zohoInstance
      .getEventsByRange(startDate, endDate)
      .catch((error) => {
        console.error('❌ Error específico en getEventsByRange:', error);
        throw error; // Re-lanzar para que sea capturado por el catch principal
      });

    console.log('✅ Eventos obtenidos exitosamente:', eventsData?.length || 0);

    const successResponse: SuccessResponse<GetEventsResponseData> = {
      statusCode: ReponseStatusCode.Success,
      data: { events: eventsData },
    };
    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(successResponse));
  } catch (error) {
    console.error('❌ Error en getEventsController:', error);

    // Manejar errores específicos de Zoho
    if (error && typeof error === 'object' && 'error' in error) {
      console.error('❌ Error de Zoho API:', error);
    }

    // Verificar si es un error de autenticación
    if (
      error instanceof Error &&
      error.message.includes('ZOHO_REAUTH_REQUIRED')
    ) {
      const errorResponse: ErrorResponse = {
        message: 'Se requiere re-autenticación con Zoho Calendar',
        statusCode: ReponseStatusCode.Unauthorized,
      };
      res.writeHead(HttpStatus.UNAUTHORIZED, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
