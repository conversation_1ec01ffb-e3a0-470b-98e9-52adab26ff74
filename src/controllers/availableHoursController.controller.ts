import QueryString from 'qs';
import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import { TimeRange } from '~/model/CompanyModel';
import {
  extractHours,
  getFilteredAvailableSlotsForDay,
} from '~/utils/functions';

interface AvailableHoursRequestBody {
  /**
   * Fecha en formato 'YYYY-MM-DD'
   */
  date: string;
  workTime: TimeRange[];
}

interface AvailableHoursResponse {
  availableHours: string[];
}

/**
 * Endpoint para obtener los horarios hábiles de un día.
 *
 * Espera en el body de la petición un objeto con:
 * - date: string (formato 'YYYY-MM-DD')
 * - workTime: TimeInterval[] (arreglo con los intervalos de trabajo del día)
 */
export const availableHoursController = async (
  bot: BotType,
  req: RequestType<{}, AvailableHoursRequestBody>,
  res: ResponseType
) => {
  try {
    // Extrae la parte de la query string de la URL y la convierte en un objeto.
    // Si no existe query string, se utiliza una cadena vacía.
    const parsedQuery = QueryString.parse(req.url?.split('?')[1] || '');
    const { date, workTime } = parsedQuery as { date: string; workTime: any[] };

    if (!date || !workTime) {
      const errorResponse: ErrorResponse = {
        message: 'Parámetros inválidos. Se requiere "date" y "workTime".',
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Convertir cada objeto de workTime: start y end vienen como strings, por lo que se convierten a números.
    const parsedWorkTime: TimeRange[] = workTime.map((wt: any) => ({
      id: wt.id,
      start: Number(wt.start),
      end: Number(wt.end),
    }));

    const availableSlots = await getFilteredAvailableSlotsForDay(
      parsedWorkTime,
      date
    );

    const formattedHours = availableSlots ? extractHours(availableSlots) : [];

    const response: SuccessResponse<AvailableHoursResponse> = {
      statusCode: ReponseStatusCode.Success,
      data: {
        availableHours: formattedHours,
      },
    };

    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } catch (error) {
    console.error('Error al obtener los horarios disponibles:', error);
    const errorResponse: ErrorResponse = {
      message: 'Error interno del servidor al obtener los horarios disponibles',
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
