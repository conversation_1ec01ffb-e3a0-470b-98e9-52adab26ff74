import {
  BotType,
  ErrorResponse,
  HttpStatus,
  ReponseStatusCode,
  RequestType,
  ResponseType,
  SuccessResponse,
} from '~/model/ApiModel';
import firebaseServiceInstance from '~/database/firebaseService';
import jwt from 'jsonwebtoken';
import {
  AppointmentStates,
  ConfirmAppointmentUpdateRequestBody,
  ConfirmAppointmentUpdateResponse,
  DeleteAppointmentParams,
} from '~/model/AppointmentModel';
import { AppointmentUpdateMessages } from '~/model/AppointmentTokenModel';
import { cancelAppointment } from '~/services/appointmentService';

export const confirmAppointmentUpdateController = async (
  bot: BotType,
  req: RequestType<ConfirmAppointmentUpdateRequestBody>,
  res: ResponseType
) => {
  try {
    const { token, action, etag, eventUid, phoneNumber: phone } = req.body;
    if (!token || !action) {
      const errorResponse: ErrorResponse = {
        message: AppointmentUpdateMessages.MISSING_PARAMETERS,
        statusCode: ReponseStatusCode.BadRequest,
      };
      res.writeHead(HttpStatus.BAD_REQUEST, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Recuperar el documento del token a partir del código corto
    const tokenDoc = await firebaseServiceInstance.getAppointmentToken(token);
    if (!tokenDoc) {
      const errorResponse: ErrorResponse = {
        message: AppointmentUpdateMessages.CODE_INVALID,
        statusCode: ReponseStatusCode.Unauthorized,
      };
      res.writeHead(HttpStatus.UNAUTHORIZED, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Validar si el token ya fue utilizado
    if (tokenDoc.used) {
      const errorResponse: ErrorResponse = {
        message: AppointmentUpdateMessages.CODE_ALREADY_USED,
        statusCode: ReponseStatusCode.Unauthorized,
      };
      res.writeHead(HttpStatus.UNAUTHORIZED, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Validar expiración (suponiendo que tokenDoc.expiresAt es un timestamp en ms)
    if (tokenDoc.expiresAt && Date.now() > tokenDoc.expiresAt) {
      const errorResponse: ErrorResponse = {
        message: AppointmentUpdateMessages.CODE_INVALID,
        statusCode: ReponseStatusCode.Unauthorized,
      };
      res.writeHead(HttpStatus.UNAUTHORIZED, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Verificar el JWT almacenado en tokenDoc.token
    const secret = process.env.JWT_SECRET || 'TU_SECRETO_MUY_SEGURO';
    let decoded: any;
    try {
      decoded = jwt.verify(tokenDoc.token, secret);
    } catch (error) {
      const errorResponse: ErrorResponse = {
        message: AppointmentUpdateMessages.CODE_INVALID,
        statusCode: ReponseStatusCode.Unauthorized,
      };
      res.writeHead(HttpStatus.UNAUTHORIZED, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Determinar el nuevo estado basado en la acción
    const newStatus: AppointmentStates =
      action === 'accept'
        ? AppointmentStates.Aceptada
        : AppointmentStates.Rechazada;

    // Actualizar la cita en Firestore (se asume que updateAppointmentStatus utiliza decoded.appointmentId)
    // ? solo se actualizaria el estado de la cita, ya que previamente la cita ya se habia creado
    // ? Crear metodo en firebaseServiceInstance
    const updateResult = await firebaseServiceInstance.updateAppointmentStatus(
      decoded.appointmentId,
      newStatus
    );
    if (!updateResult) {
      const errorResponse: ErrorResponse = {
        message: AppointmentUpdateMessages.INTERNAL_SERVER_ERROR,
        statusCode: ReponseStatusCode.ServerError,
      };
      res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    // Marcar el token como usado para invalidarlo
    await firebaseServiceInstance.markAppointmentTokenUsed(token);

    const successMessage =
      action === 'accept'
        ? AppointmentUpdateMessages.APPOINTMENT_CONFIRMED
        : AppointmentUpdateMessages.APPOINTMENT_REJECTED;

    if (action === 'reject') {
      const appointmentParams: DeleteAppointmentParams = {
        eventUid,
        etag,
        phone,
        isRejected: true,
      };
      await cancelAppointment(appointmentParams);
    }

    const response: SuccessResponse<ConfirmAppointmentUpdateResponse> = {
      statusCode: ReponseStatusCode.Success,
      data: { message: successMessage },
    };

    // TODO: mandar mensaje al usuario de la accion realizada
    // await bot.sendMessage(phoneNumber, updatedMessage, {});

    res.writeHead(HttpStatus.OK, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } catch (error) {
    console.error('Error en confirmAppointmentUpdateController:', error);
    const errorResponse: ErrorResponse = {
      message: AppointmentUpdateMessages.INTERNAL_SERVER_ERROR,
      statusCode: ReponseStatusCode.ServerError,
    };
    res.writeHead(HttpStatus.INTERNAL_SERVER_ERROR, {
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify(errorResponse));
  }
};
