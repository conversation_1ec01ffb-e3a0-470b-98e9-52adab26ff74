import { addKeyword, EVENTS } from '@builderbot/bot';
import { getFlow } from '../flowHandler';

const preguntasYRespuestas = [
  {
    pregunta: '¿Cuánto es el costo por sesión?',
    respuesta:
      '💵 La inversión por cada sesión es de 500 pesos. Valoramos tu bienestar y nos esforzamos por ofrecer un servicio de alta calidad y profesionalismo. Si tienes alguna duda o requieres información sobre paquetes o promociones, no dudes en preguntar. ¡Estamos aquí para apoyarte! 😊💡',
  },
  {
    pregunta: '¿Cuánto dura cada sesión?',
    respuesta:
      '🕰️ Cada sesión tiene una duración de aproximadamente 50 minutos. ¡Estamos aquí para apoyarte! 💬',
  },
  {
    pregunta: '¿Ofrecen terapia en línea?',
    respuesta:
      '🌐 Sí, ofrecemos sesiones en línea para aquellos que no pueden asistir en persona o prefieren la comodidad de su hogar. 💻🤗',
  },
  {
    pregunta:
      '¿Es confidencial la información que comparto durante la terapia?',
    respuesta:
      '🤐 Absolutamente. Respetamos y valoramos tu privacidad. Todo lo que compartas durante la terapia se mantiene en estricta confidencialidad, a menos que exista un riesgo inminente para ti o para otros. Tu bienestar es nuestra prioridad. 🔒❤️',
  },
  {
    pregunta: '¿Puedo llevar a alguien conmigo a la sesión?',
    respuesta:
      '🤔 Claro, si te sientes más cómodo, puedes llevar a alguien contigo. Sin embargo, es importante que nos lo comuniques con anticipación para asegurarnos de que la sesión sea productiva para ti. 👥💬',
  },
];

export const flowDudas = addKeyword(EVENTS.ACTION)
  .addAnswer(
    [
      '¡Hola! 👋 Aquí tienes algunas de las preguntas más frecuentes que recibimos.',
      'Por favor, selecciona la que más se asemeje a tu inquietud y con gusto te brindaré la respuesta. 😊',
      '',
      `O escribe "*menu*" para volver al menu principal`,
    ],
    null,
    async (ctx, { flowDynamic }) => {
      const questions = [];

      preguntasYRespuestas.forEach((q, index) => {
        questions.push({ body: `${index + 1} ${q.pregunta}` });
      });

      await flowDynamic(questions);
    }
  )
  .addAction(
    { capture: true },
    async (ctx, { fallBack, flowDynamic, gotoFlow }) => {
      const numberOfQuestions = preguntasYRespuestas.map((_, i) => i + 1 + '');

      if (ctx.body.toLowerCase() === 'menu') {
        return gotoFlow(getFlow('flowPrincipalSinSaludo'));
      }

      if (!numberOfQuestions.includes(ctx.body)) {
        return fallBack(
          `Porfavor ingresa una opción valida 😊\n\nO escribe "*menu*" para volver al menu principal.`
        );
      } else {
        const respuesta = preguntasYRespuestas[+ctx.body - 1].respuesta;

        await flowDynamic([
          {
            body: `${respuesta}`,
          },
        ]);
        return fallBack(
          `Si quieres saber la respuesta a otra pregunta ingresa el numero correspondiente 😊\n\nO escribe "*menu*" para volver al menu principal.`
        );
      }
    }
  );
