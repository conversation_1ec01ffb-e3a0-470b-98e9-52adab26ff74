// Code to prevent circular dependencies for certain files
// Initialize an empty object that will store all flows.
let flows: Record<string, any> = {};

/**
 * Sets (or overwrites) the current flows.
 *
 * @param {Record<string, any>} newFlows - The new set of flows.
 */
export function setFlows(newFlows: Record<string, any>): void {
  flows = newFlows;
}

/**
 * Gets a specific flow by its name.
 *
 * @param {string} name - The name of the flow to retrieve.
 * @returns {any} - The requested flow.
 */
export function getFlow(name: string): any {
  return flows[name];
}
