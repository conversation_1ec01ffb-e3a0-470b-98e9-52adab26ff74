import { createFlow } from '@builderbot/bot';
import { flowPrincipal } from './bienvenida/principal.flow';
import { setFlows } from './flowHandler';
import { flowPrincipalSinSaludo } from './bienvenida/principalSinSaludo.flow';
import { flowDudas } from './dudas/dudas.flow';
import { flowAgenda } from './citas/agenda.flow';
import { flowChooseMonth } from './citas/chooseMonth.flow';
import { flowWeek } from './citas/chooseWeek.flow';
import { flowDay } from './citas/chooseDay.flow';
import { flowChooseTime } from './citas/chooseTime.flow';
import { flowConfirmation } from './citas/confirmation.flow';
import { flowUserHasAppointment } from './citas/userHasAppointment.flow';
import { flowCancelAppointment } from './citas/cancelAppointment.flow';
import { flowModifyAppointment } from './citas/modifyAppointment.flow';

setFlows({
  flowPrincipal,
  flowPrincipalSinSaludo,
  flowChooseMonth,
  flowWeek,
  flowDay,
  flowChooseTime,
  flowConfirmation,
  flowAgenda,
  flowUserHasAppointment,
  flowCancelAppointment,
  flowModifyAppointment,
  flowDudas,
});

export default createFlow([
  flowPrincipal,
  flowPrincipalSinSaludo,
  flowChooseMonth,
  flowWeek,
  flowDay,
  flowChooseTime,
  flowConfirmation,
  flowAgenda,
  flowUserHasAppointment,
  flowCancelAppointment,
  flowModifyAppointment,
  flowDudas,
]);
