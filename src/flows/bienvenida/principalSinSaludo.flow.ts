import { addKeyword, EVENTS } from '@builderbot/bot';
import { getFlow } from '../flowHandler';

export const flowPrincipalSinSaludo = addKeyword(EVENTS.ACTION).addAnswer(
  [
    'Porfavor elige la opcion que deseas 😃',
    '*1* Agendar cita',
    '*2* Preguntas frecuentes',
  ],
  { capture: true },
  async (ctx, { fallBack, gotoFlow }) => {
    if (!['1', '2'].includes(ctx.body)) {
      return fallBack(
        'Opcion no valida, porfavor ingresa una de las 3 opciones 😊'
      );
    }

    if (ctx.body === '1') {
      return gotoFlow(getFlow('flowAgenda'));
    } else if (ctx.body === '2') {
      return gotoFlow(getFlow('flowDudas'));
    }
  }
);
