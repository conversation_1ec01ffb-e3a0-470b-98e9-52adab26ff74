import { addKeyword, EVENTS } from '@builderbot/bot';
import { getFlow } from '../flowHandler';
import { useCompanyStore } from '~/store/CompanyStore';

export const flowPrincipal = addKeyword([EVENTS.WELCOME])
  .addAction(async (_, { flowDynamic }) => {
    await flowDynamic([
      {
        body: `¡Bienvenido a ${
          useCompanyStore.getState().companyData?.companyName
        }! 😊✨`,
      },
    ]);
  })
  .addAnswer(
    [
      '"La mente es como un paracaídas, solo funciona si está abierta." 🌈🪂 ¿Sabías que la psicología nos ayuda a comprendernos mejor y a encontrar soluciones para alcanzar nuestro bienestar emocional? Estamos aquí para apoyarte en tu camino hacia una salud mental integral.',
      '',
      'Por favor, elige una de las opciones disponibles:',
      '',
      '*1* Agendar cita 📅',
      '*2* Preguntas frecuentes ❔',
      // '*3* Contactar a Luis 🙍',
      '',
    ],
    { capture: true },
    async (ctx, { fallBack, gotoFlow }) => {
      if (!['1', '2', '3'].includes(ctx.body)) {
        return fallBack(
          'Opcion no valida, porfavor ingresa una de las 3 opciones 😊'
        );
      }

      if (ctx.body === '1') {
        return gotoFlow(getFlow('flowAgenda'));
      } else if (ctx.body === '2') {
        return gotoFlow(getFlow('flowDudas'));
      }
      // if (ctx.body === '3') {
      //   return gotoFlow(flowCrearGrupo);
      // }
    }
  );
