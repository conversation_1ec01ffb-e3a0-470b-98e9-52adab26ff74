import { addKeyword, EVENTS } from '@builderbot/bot';
import { monthMap, numberToEmoji, WEEK_DAYS } from '~/utils/consts';
import { getRandomDelay } from '~/utils/functions';
import { getFlow } from '../flowHandler';
import { useCompanyStore } from '~/store/CompanyStore';

// TODO: agregar validaciones si por alguna razon no hay dias disponibles
export const flowDay = addKeyword(EVENTS.ACTION)
  .addAnswer(
    'Selecciona el *dia* 🌅',
    null,
    async (ctx, { flowDynamic, state }) => {
      const userOption = ctx.body;
      const companyData = useCompanyStore.getState().companyData;

      const selectedWeekDays = state.get('weeks')?.[userOption];

      const availableWorkDays = WEEK_DAYS.map((day) => {
        const selectedDay = companyData.workDays[day];

        return (
          selectedDay.isAvailable && selectedDay.dayName.toLocaleLowerCase()
        );
      }).filter((weekDay) => weekDay);

      console.log({ availableWorkDays, selectedWeekDays });

      // TODO: revisar como se puede mejorar
      const filteredWorkDays = selectedWeekDays.filter((day) => {
        const dayName = day.split(' ')?.[0].toLowerCase();
        console.log({ dayName });
        const dayNumber = parseInt(day.split(' ')?.[1], 10);

        // Get the selected month index and retrieve month info
        const selectedMonthIndex = parseInt(state.get('selectedMonth'), 10) - 1;
        const monthInfo = state.get('months')[selectedMonthIndex];
        const monthName = monthInfo.month;
        const year = monthInfo.year;

        const monthNumber = monthMap[monthName];

        // Create a Date object for the current day
        const selectedDate = new Date(year, monthNumber - 1, dayNumber);

        // Filtrado de los dias de descanso
        const isDayOff = companyData.daysOff.some((timestamp) => {
          const dayOffDate = timestamp.toDate(); // Convert Timestamp to Date

          return (
            selectedDate.getFullYear() === dayOffDate.getFullYear() &&
            selectedDate.getMonth() === dayOffDate.getMonth() &&
            selectedDate.getDate() === dayOffDate.getDate()
          );
        });

        // Return true if the day is a working day and not a day off
        return availableWorkDays.includes(dayName) && !isDayOff;
      });

      console.log('filteredWorkDays');
      console.log(filteredWorkDays);

      await state.update({
        selectedWeek: userOption,
        weekDays: filteredWorkDays,
      });

      for (let i = 0; i < filteredWorkDays.length; i++) {
        const day = filteredWorkDays[i];
        await flowDynamic(`*${numberToEmoji[i + 1]}* -> ${day}`, {
          delay: getRandomDelay(),
        });
      }
    }
  )
  .addAction({ capture: true }, async (ctx, { state, fallBack, gotoFlow }) => {
    const days = state.get('weekDays');

    if (!(+ctx.body - 1 in days)) {
      if (ctx.body.toLowerCase() === 'menu')
        return gotoFlow(getFlow('flowPrincipalSinSaludo'));

      return fallBack(
        'Porfavor ingresa una opcion valida.\n\nO escribe "*menu*" para volver al menu principal'
      );
    } else {
      return gotoFlow(getFlow('flowChooseTime'));
    }
  });
