// const { flowChooseMonth } = require('./citas/flowAgregarOModificarCita');
import { addKeyword, EVENTS } from '@builderbot/bot';
import firebaseServiceInstance from '~/database/firebaseService';
import { getFlow } from '../flowHandler';
import { AppointmentStates } from '~/model/AppointmentModel';
import { generateNgrams } from '~/utils/functions';

// const { getFlow } = require('./flowHandler');

export const flowAgenda = addKeyword(EVENTS.ACTION).addAction(
  // 'Porfavor sigue los siguientes pasos para agendar tu cita 😃',
  null,
  async (ctx, { state, gotoFlow }) => {
    const { data, hasAppointment, hasAppointmentExpired } =
      await firebaseServiceInstance.userAppointment(ctx.from);

    console.log({
      data,
      hasAppointment,
      hasAppointmentExpired,
    });
    await firebaseServiceInstance.userExistsOrAdd({
      id: ctx.from,
      name: ctx.name,
      ngrams: generateNgrams(ctx.name),
      phoneNumber: ctx.from,
    });

    await state.update({
      hasAppointmentScheduled: hasAppointment && !hasAppointmentExpired,
      startAppointment: data?.start,
      userData: {
        hasAppointment,
        hasAppointmentExpired,
        ...data,
      },
    });

    // TODO: add types to myState
    const myState = state.getMyState();
    console.log({ myState });

    if (
      // Si funciona esta logica?
      myState.hasAppointmentScheduled &&
      myState.userData.status !== AppointmentStates.Cancelada &&
      myState.userData.status !== AppointmentStates.Rechazada
    ) {
      // await state.update({ isNewAppointment: false });
      return gotoFlow(getFlow('flowUserHasAppointment'));
    } else {
      await state.update({ isNewAppointment: true });
      return gotoFlow(getFlow('flowChooseMonth'));
    }
  }
);
