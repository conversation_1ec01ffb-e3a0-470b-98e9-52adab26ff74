import { addKeyword, EVENTS } from '@builderbot/bot';
import { numberToEmoji } from '~/utils/consts';
import { getMonthsToDisplay, getRandomDelay } from '~/utils/functions';
import { getFlow } from '../flowHandler';

export const flowChooseMonth = addKeyword(EVENTS.ACTION)
  .addAnswer(
    'Por favor indica el número del *mes* en el que deseas agendar 😊',
    null,
    async (_, { flowDynamic, state }) => {
      try {
        const months = getMonthsToDisplay();

        await state.update({ months: months });

        for (let i = 0; i < months.length; i++) {
          const currentMonth = months[i];

          await flowDynamic(`${numberToEmoji[i + 1]} *${currentMonth.month}*`, { delay: getRandomDelay() });
        }
      } catch (err) {
        console.log(err);
      }
    }
  )
  .addAction({ capture: true }, async (ctx, { state, fallBack, gotoFlow }) => {
    const currentMonths = state.get('months');
    const selectedMonth = ctx.body;
    const monthNumbers = Object.keys(currentMonths).map((month) => String(+month + 1));

    if (!(monthNumbers.includes(selectedMonth))) {
      if (selectedMonth.toLowerCase() === 'menu')
        return gotoFlow(getFlow('flowPrincipalSinSaludo'));

      return fallBack(
        'Porfavor ingresa una opcion valida.\n\nO escribe "*menu*" para volver al menu principal'
      );
    } else {
      return gotoFlow(getFlow('flowWeek'));
    }
  });
