import { addKeyword, EVENTS } from '@builderbot/bot';
import { getFlow } from '../flowHandler';
import {
  getFormattedWeeks,
  getRandomDelay,
  getWeeksOfMonth,
} from '~/utils/functions';
import { MonthYear } from '~/model/FunctionsModel';
import { MonthsOfYear } from '~/model/AppointmentModel';

// TODO: no colocar la semana cuando no haya dias disponibles
export const flowWeek = addKeyword(EVENTS.ACTION)
  .addAnswer(
    'Selecciona el numero de la *semana* en la cual te gustaria agendar',
    null,
    async (ctx, { flowDynamic, state }) => {
      const months: MonthYear[] = state.get('months');
      const monthIndex = +ctx.body - 1;
      const selectedMonthObject = months[monthIndex];
      const selectedMonthNumber: MonthsOfYear =
        +MonthsOfYear[selectedMonthObject.month];

      const weeks = getWeeksOfMonth(
        selectedMonthObject.year,
        selectedMonthNumber
      );
      const formattedWeeks = getFormattedWeeks(weeks.weeks);

      await state.update({
        selectedMonth: ctx.body,
        currentMonthSelected: selectedMonthObject.month,
        currentYearSelected: selectedMonthObject.year,
        weeks: weeks.weeks,
      });

      await flowDynamic(`*${weeks.monthName}*`, { delay: getRandomDelay() });

      for (const week of formattedWeeks) {
        await flowDynamic(week, { delay: getRandomDelay() });
      }
    }
  )
  .addAction({ capture: true }, async (ctx, { state, fallBack, gotoFlow }) => {
    const currentWeeks = state.get('weeks');
    const weekSelection = ctx.body;

    if (!(weekSelection in currentWeeks)) {
      if (weekSelection.toLowerCase() === 'menu')
        return gotoFlow(getFlow('flowPrincipalSinSaludo'));

      return fallBack(
        'Porfavor ingresa una opcion valida.\n\nO escribe "*menu*" para volver al menu principal'
      );
    } else {
      return gotoFlow(getFlow('flowDay'));
    }
  });
