import { addKeyword, EVENTS } from '@builderbot/bot';
import {
  addOneHourToISODate,
  convertDateToISO8601,
  generateNgrams,
  getRandomDelay,
} from '~/utils/functions';
import zohoInstance from '~/calendar/ClassZohoApi';
import { getFlow } from '../flowHandler';
import firebaseServiceInstance from '~/database/firebaseService';
import { Timestamp } from 'firebase-admin/firestore';
import { Appointment, AppointmentStates } from '~/model/AppointmentModel';
import { format } from 'date-fns';

// TODO: Hay un edge case que es que cuando no se agrega la cita a firestore esta si se agregua al calendario, ver la manera de solucionarlo
// Tal vez sea eliminando la cita que se acaba de crear???

const realNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
];

function generateMockAppointmentData(index) {
  // Generate date (spread over the next 40 days)
  const date = new Date();
  date.setDate(date.getDate() + index);
  const year = date.getFullYear();
  const month = ('0' + (date.getMonth() + 1)).slice(-2);
  const day = ('0' + date.getDate()).slice(-2);
  const splitedDate = `${year}-${month}-${day}`;

  // Generate start and end times
  const startHour = 9 + (index % 8); // Cycle through hours 9 to 16
  const endHour = startHour + 1;
  const splitedStartTime = `${('0' + startHour).slice(-2)}:00:00`;
  const splitedEndTime = `${('0' + endHour).slice(-2)}:00:00`;
  const timeRange = `${splitedStartTime}-${splitedEndTime}`;

  const startDateTime = `${splitedDate}T${splitedStartTime}-06:00`;
  const endDateTime = `${splitedDate}T${splitedEndTime}-06:00`;

  // Seleccionar un nombre real aleatorio
  const realName = realNames[Math.floor(Math.random() * realNames.length)];

  // Generate other data
  const nuevaCita = {
    start: startDateTime,
    end: endDateTime,
    user: '521' + Math.floor(1000000000 + Math.random() * 9000000000),
    nombre: realName,
    ngrams: generateNgrams(
      realName.normalize('NFD').replace(/[\u0300-\u036f]/g, '')
    ),
    timestampDate: Timestamp.fromDate(new Date(startDateTime)),
    eventUid: `eventUid${index}`,
    viewEventURL: `https://calendar.example.com/event/${index}`,
    etag: `${Date.now()}${index}`,
    status: [
      AppointmentStates.Confirmada,
      AppointmentStates.Cancelada,
      AppointmentStates.Pendiente,
    ][Math.floor(Math.random() * 3)],
    cancelledAt: null,
  };

  return { splitedDate, timeRange, nuevaCita };
}

async function insertMockAppointments() {
  for (let i = 0; i < 40; i++) {
    const { splitedDate, timeRange, nuevaCita } =
      generateMockAppointmentData(i);
    console.log(
      `Inserting appointment ${i + 1}:`,
      splitedDate,
      timeRange,
      nuevaCita
    );

    try {
      const result = await firebaseServiceInstance.addOrUpdateAppointment(
        splitedDate,
        timeRange,
        nuevaCita
      );
      console.log(`Appointment ${i + 1} inserted successfully.`);
    } catch (error) {
      console.error(`Error inserting appointment ${i + 1}:`, error);
    }
  }
}

export const flowConfirmation = addKeyword(EVENTS.ACTION).addAction(
  { capture: false },
  async (ctx, { flowDynamic, gotoFlow, state }) => {
    await state.update({ selectedTime: ctx.body });

    const myState = state.getMyState();

    const {
      currentMonthSelected,
      currentYearSelected,
      currentDaySelected,
      selectedTime,
      availableTimes,
      isNewAppointment,
      userData,
      updateTokenState,
    } = myState;

    const { tokenCode } = userData;

    const time = availableTimes[selectedTime - 1]; // Users time selected
    console.log({
      currentMonthSelected,
      currentDaySelected,
      time,
      currentYearSelected,
    });

    const startDate = convertDateToISO8601(
      currentDaySelected,
      currentMonthSelected,
      currentYearSelected,
      time
    );
    const endDate = addOneHourToISODate(startDate);
    console.log({ startDate, endDate });

    const splitedDate = startDate.split('T')[0]; // 2023-08-20
    const splitedStartTime = format(new Date(startDate), 'h:mm a');
    const splitedEndTime = format(new Date(endDate), 'h:mm a');

    console.log('isSlotAvailable!!!!');
    // Ya tenia este codigo? :))))) estandarizar la forma en que valida si el slot es valido
    const isSlotAvailable = await zohoInstance.isSlotAvailable(
      startDate,
      endDate
    );
    console.log({ isSlotAvailable, isNewAppointment });

    if (!isSlotAvailable) {
      await flowDynamic(
        'Ups, parece que ese horario ya está ocupado 😕. Por favor, selecciona otro.',
        {
          delay: getRandomDelay(),
        }
      );

      return gotoFlow(getFlow('flowPrincipal'));
    }

    if (isNewAppointment) {
      try {
        const calendarCall = await zohoInstance.createEvent(
          startDate,
          endDate,
          ctx.pushName
        );

        if (calendarCall.status) {
          const eventDataResult = calendarCall.eventData;

          console.log('eventData!!!!');
          console.log(eventDataResult);

          const nuevaCita: Appointment = {
            start: startDate,
            end: endDate,
            user: ctx.from,
            nombre: ctx.pushName,
            ngrams: generateNgrams(ctx.pushName),
            timestampDate: Timestamp.fromDate(new Date(startDate)),
            eventUid: eventDataResult.uid,
            viewEventURL: eventDataResult.viewEventURL,
            etag: eventDataResult.etag,
            status: AppointmentStates.Pendiente,
            cancelledAt: null,
          };

          console.log(
            splitedDate,
            splitedStartTime + '-' + splitedEndTime,
            nuevaCita
          );

          console.log('CITA NUEVA!');
          // Agrega la cita a firestore
          const newResult =
            await firebaseServiceInstance.addOrUpdateAppointment(
              splitedDate,
              splitedStartTime + '-' + splitedEndTime,
              nuevaCita
            );

          // await insertMockAppointments();

          if (newResult) {
            await flowDynamic(
              `¡Excelente! la cita ah quedado agendada para el:\n\n${currentDaySelected} de ${currentMonthSelected} del ${currentYearSelected}, a las ${time}, espere confirmación, bonito dia 🤗\n\nEscribe "*menu*" para volver al menu principal.`,
              {
                delay: getRandomDelay(),
              }
            );
          }
        }

        await firebaseServiceInstance.userExistsOrAdd({
          id: ctx.from,
          name: ctx.name,
          ngrams: generateNgrams(ctx.name),
          phoneNumber: ctx.from,
        });
      } catch (error) {
        console.log(error);
        await flowDynamic(
          'Lo sentimos 😔 ocurrio un error al intentar agendar la fecha, intente de nuevo más tarde.',
          {
            delay: getRandomDelay(),
          }
        );

        return gotoFlow(getFlow('flowPrincipal'));
      }
    } else {
      try {
        if (updateTokenState) {
          await firebaseServiceInstance.markAppointmentTokenUsed(tokenCode);
        }

        const calendarCall = await zohoInstance.updateEvent({
          eventUID: userData.eventUid,
          startDate: startDate,
          endDate: endDate,
          user: ctx.pushName,
          etag: userData.etag,
        });
        console.log('.....................');
        console.log(calendarCall);
        if (calendarCall.status) {
          const eventDataResult = calendarCall.eventData;
          console.log('eventData updated!!!!');
          console.log(eventDataResult);
          const citaModificada: Appointment = {
            start: startDate,
            end: endDate,
            user: ctx.from,
            nombre: ctx.pushName,
            ngrams: generateNgrams(ctx.pushName),
            timestampDate: Timestamp.fromDate(new Date(startDate)),
            eventUid: eventDataResult.uid,
            viewEventURL: eventDataResult.viewEventURL,
            etag: eventDataResult.etag,
            status: AppointmentStates.Pendiente,
            cancelledAt: null,
          };
          console.log('CITA ACTUALIZADA!');
          // Agrega la cita a firestore
          const updateResult =
            await firebaseServiceInstance.addOrUpdateAppointment(
              splitedDate,
              splitedStartTime + '-' + splitedEndTime,
              citaModificada
            );
          if (updateResult) {
            await flowDynamic(
              `Tu cita se actualizo para el:\n\n${currentDaySelected} de ${currentMonthSelected} del ${currentYearSelected}, a las ${time}, espere confirmación, bonito dia 🤗\n\nEscribe "*menu*" para volver al menu principal.`,
              {
                delay: getRandomDelay(),
              }
            );
          }
        }
      } catch (error) {
        console.log(error);
        await flowDynamic(
          `Lo sentimos no pudimos actualizar su cita, porfavor intente de nuevo mas tarde.`,
          {
            delay: getRandomDelay(),
          }
        );

        return gotoFlow(getFlow('flowPrincipal'));
      } finally {
        await state.update({
          updateTokenState: false,
        });
      }
    }
  }
);
