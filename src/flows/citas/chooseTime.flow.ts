import { addKeyword, EVENTS } from '@builderbot/bot';
import { getFlow } from '../flowHandler';
import { monthMap, numberToEmoji, WEEK_DAYS_MAP } from '~/utils/consts';
import {
  extractHours,
  getFilteredAvailableSlotsForDay,
  getRandomDelay,
} from '~/utils/functions';
import { useCompanyStore } from '~/store/CompanyStore';
import { DaySchedule } from '~/model/CompanyModel';

/**
 * Solo revisara los horarios disponibles por el momento, mas adelante tendra que revisar si no hay
 * horarios disponibles que no muestre el dia :)
 */
export const flowChooseTime = addKeyword(EVENTS.ACTION)
  .addAnswer(
    'Selecciona la hora 🕗',
    null,
    async (ctx, { flowDynamic, state, gotoFlow }) => {
      const userOption = ctx.body;
      const workDays = useCompanyStore.getState().companyData.workDays;
      const dayIndex = +userOption - 1;
      const myState = state.getMyState();
      const { weekDays, currentMonthSelected, currentYearSelected } = myState;

      const daySelected = weekDays[dayIndex];
      const currentWeekDaySelected: string = daySelected
        .split(' ')?.[0] // Get just the day name, without the number
        .toLowerCase();
      console.log({ daySelected, workDays });

      const currentObjectDay: DaySchedule =
        workDays[WEEK_DAYS_MAP[currentWeekDaySelected]];

      console.log(currentObjectDay);
      console.log(currentObjectDay.workTime);

      // Supongamos que extraes el número del día y ya tienes currentYearSelected y currentMonthSelected
      const dayNumber = daySelected.split(' ')[1]; // "15"
      const dateAsString = `${currentYearSelected}-${monthMap[currentMonthSelected]}-${dayNumber}`;

      const availableTimes = await getFilteredAvailableSlotsForDay(
        currentObjectDay.workTime,
        dateAsString
      );

      console.log('Slots disponibles:', availableTimes);
      const formattedTime = extractHours(availableTimes);
      console.log('availableTimes: ', formattedTime);

      await state.update({
        currentDaySelected: daySelected,
        availableTimes: formattedTime,
        currentObjectDay,
      });

      if (formattedTime.length === 0) {
        // TODO: esto es una solucion temporal, si 1 un dia no tiene horarios disponibles no deberia aparecer
        // Pero aun no se como hacerlo de la manera mas eficiente
        await flowDynamic(
          'No hay horarios disponibles para ese día 😔 porfavor elige otra fecha.',
          {
            delay: getRandomDelay(),
          }
        );
        return gotoFlow(getFlow('flowChooseMonth'));
      } else {
        for (let i = 0; i < formattedTime.length; i++) {
          const time = formattedTime[i];

          await flowDynamic(`${numberToEmoji[i + 1]} ${time}`, {
            delay: getRandomDelay(),
          });
        }
      }
    }
  )
  .addAction({ capture: true }, async (ctx, { state, fallBack, gotoFlow }) => {
    const availableTimes = state.get('availableTimes');
    const times = availableTimes?.map((_, index) => index + 1 + '');

    // TODO: agregar validacion de fecha

    if (!times.includes(ctx.body)) {
      if (ctx.body.toLowerCase() === 'menu')
        return gotoFlow(getFlow('flowPrincipalSinSaludo'));

      return fallBack(
        'Porfavor ingresa una opcion valida.\n\nO escribe "*menu*" para volver al menu principal'
      );
    } else {
      return gotoFlow(getFlow('flowConfirmation'));
    }
  });
