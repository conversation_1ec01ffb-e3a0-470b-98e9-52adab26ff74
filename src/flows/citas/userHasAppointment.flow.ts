import { addKeyword, EVENTS } from '@builderbot/bot';
import { formatDateForMexicoCity } from '~/utils/functions';
import { getFlow } from '../flowHandler';
import { AppointmentStates } from '~/model/AppointmentModel';
import firebaseServiceInstance from '~/database/firebaseService';

export const flowUserHasAppointment = addKeyword(EVENTS.ACTION)
  .addAnswer(
    '¡Oh! 😮 Ya tienes una cita agendada 😃',
    null,
    async (_, { flowDynamic, state }) => {
      const myState = state.getMyState();
      const { userData } = myState;
      await state.update({
        updateTokenState: false,
      });

      // ? Que hacer cuando el token expire y el usuario no acepto o rechazo el cambio de fecha,
      // ? o dejarlo asi, actualmente solo muestra que tienes una cita agendada
      if (userData.tokenExpiresAt && Date.now() < userData.tokenExpiresAt) {
        const tokenResult = await firebaseServiceInstance.getAppointmentToken(
          userData.tokenCode
        );

        if (!tokenResult.used) {
          await state.update({
            updateTokenState: true,
          });

          await flowDynamic(
            `Tenemos una actualización en tu cita programada para el *${formatDateForMexicoCity(
              myState.startAppointment
            )}*.\n\nPor favor, revisa los detalles y confirma tu asistencia haciendo clic en el siguiente enlace: ${
              userData.confirmationUrl
            }
            \nPorfavor escribe el número que necesites\n\n1️⃣ Cancelar\n\n2️⃣ Modificar cita\n\n3️⃣ Volver al menu principal`
          );

          return;
        }
      }

      await flowDynamic(
        `Para el *${formatDateForMexicoCity(myState.startAppointment)}*.${
          userData.status === AppointmentStates.Aceptada
            ? '\n\n*Se te enviara un mensaje de confirmacion 1 día antes de tu cita.*'
            : ''
        }\n\nPorfavor escribe el número que necesites\n\n1️⃣ Cancelar\n\n2️⃣ Modificar cita\n\n3️⃣ Volver al menu principal`
      );
    }
  )
  .addAction({ capture: true }, async (ctx, { fallBack, gotoFlow }) => {
    const correctOptions = ['1', '2', '3'];

    if (!correctOptions.includes(ctx.body)) {
      return fallBack('Porfavor ingresa una opción correcta 😃');
    } else {
      switch (ctx.body) {
        case '1':
          return gotoFlow(getFlow('flowCancelAppointment'));
        case '2':
          return gotoFlow(getFlow('flowModifyAppointment'));
        case '3':
          return gotoFlow(getFlow('flowPrincipal'));
      }
    }
  });
