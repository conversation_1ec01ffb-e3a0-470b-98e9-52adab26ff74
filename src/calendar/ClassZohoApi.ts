import axios, { AxiosError } from 'axios';
import qs from 'qs';
import { readTokens, saveTokens } from './manageKeys';
import { convertToISOString, formatZohoDate } from '~/utils/functions';
import moment from 'moment-timezone';
import {
  ZohoAppointment,
  EventData,
  EventDetails,
  Slot,
} from '../model/ZohoCalendarModel';

function getEnvVariable(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Environment variable ${name} is not defined`);
  }
  return value;
}

class ClassZohoApi {
  private static instance: ClassZohoApi;
  private accessToken: string = '';
  private refreshToken: string = '';
  private accessTokenExpiresAt: number = 0;

  private clientId: string;
  private clientSecret: string;
  private redirectUri: string;
  private calendarUID: string;
  public clientTimeZone: string;

  constructor() {
    this.clientId = getEnvVariable('ZOHO_CLIENT_ID');
    this.clientSecret = getEnvVariable('ZOHO_CLIENT_SECRET');
    this.redirectUri = getEnvVariable('ZOHO_REDIRECT_URL');
    this.calendarUID = getEnvVariable('ZOHO_CALENDAR_UID');
    this.clientTimeZone = 'America/Mexico_City';

    const tokens = readTokens();
    if (tokens) {
      this.accessToken = tokens.accessToken;
      this.refreshToken = tokens.refreshToken;
      this.accessTokenExpiresAt = tokens.expiresIn;
    }
  }

  public static getInstance(): ClassZohoApi {
    if (!ClassZohoApi.instance) {
      ClassZohoApi.instance = new ClassZohoApi();
    }
    return ClassZohoApi.instance;
  }

  private isAccessTokenExpired(): boolean {
    return Date.now() > this.accessTokenExpiresAt;
  }

  /**
   * Verifica el estado actual de los tokens y proporciona información de diagnóstico
   */
  public getTokenStatus(): {
    hasAccessToken: boolean;
    hasRefreshToken: boolean;
    isAccessTokenExpired: boolean;
    accessTokenExpiresAt: string;
    timeUntilExpiration: string;
  } {
    const now = Date.now();
    const timeUntilExpiration =
      this.accessTokenExpiresAt > now
        ? `${Math.round((this.accessTokenExpiresAt - now) / 1000 / 60)} minutos`
        : 'Expirado';

    return {
      hasAccessToken: !!this.accessToken,
      hasRefreshToken: !!this.refreshToken,
      isAccessTokenExpired: this.isAccessTokenExpired(),
      accessTokenExpiresAt: new Date(this.accessTokenExpiresAt).toISOString(),
      timeUntilExpiration,
    };
  }

  public async firstConnectionWithAPIZoho(code: string): Promise<string> {
    const data = qs.stringify({
      code: code,
      client_id: this.clientId,
      client_secret: this.clientSecret,
      redirect_uri: this.redirectUri,
      grant_type: 'authorization_code',
    });

    try {
      const response = await axios.post(
        'https://accounts.zoho.com/oauth/v2/token',
        data,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const { access_token, refresh_token, expires_in } = response.data;
      this.accessToken = access_token;
      this.refreshToken = refresh_token;
      this.accessTokenExpiresAt = Date.now() + expires_in * 1000;

      console.log('Nuevo access_token:', response.data);

      saveTokens(access_token, refresh_token, this.accessTokenExpiresAt);
      return access_token;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error(
          'Error al obtener el access_token:',
          error.response?.data
        );
        throw error.response?.data;
      } else {
        console.error('Error:', error);
        throw error;
      }
    }
  }

  public async renewAccessToken(): Promise<string> {
    console.log('🔄 Intentando renovar access_token...');
    console.log(
      '📋 Refresh token disponible:',
      this.refreshToken ? 'Sí' : 'No'
    );
    console.log(
      '📋 Client ID:',
      this.clientId ? 'Configurado' : 'No configurado'
    );

    if (!this.refreshToken) {
      throw new Error(
        'No hay refresh_token disponible. Se requiere re-autenticación completa.'
      );
    }

    const data = qs.stringify({
      refresh_token: this.refreshToken,
      client_id: this.clientId,
      client_secret: this.clientSecret,
      grant_type: 'refresh_token',
    });

    try {
      console.log('📤 Enviando solicitud de renovación a Zoho...');
      const response = await axios.post(
        'https://accounts.zoho.com/oauth/v2/token',
        data,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const { access_token, expires_in } = response.data;
      this.accessToken = access_token;
      this.accessTokenExpiresAt = Date.now() + expires_in * 1000;

      console.log('✅ Token renovado exitosamente');
      console.log(
        '📅 Nueva expiración:',
        new Date(this.accessTokenExpiresAt).toISOString()
      );

      saveTokens(access_token, this.refreshToken, this.accessTokenExpiresAt);
      return access_token;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error(
          '❌ Error al renovar el access_token:',
          error.response?.data
        );
        console.error('📊 Status:', error.response?.status);
        console.error('📊 Headers:', error.response?.headers);

        // Si el refresh_token es inválido, lanzar un error específico
        if (
          error.response?.data?.error === 'invalid_code' ||
          error.response?.data?.error === 'invalid_grant'
        ) {
          throw new Error(
            'REFRESH_TOKEN_INVALID: El refresh_token ha expirado o es inválido. Se requiere re-autenticación completa.'
          );
        }

        throw error.response?.data;
      } else {
        console.error('❌ Error desconocido:', error);
        throw error;
      }
    }
  }

  public async getAccessTokenZoho(): Promise<string> {
    if (this.isAccessTokenExpired()) {
      console.log('⏰ TOKEN EXPIRED!!! Iniciando proceso de renovación...');
      try {
        await this.renewAccessToken();
        console.log('✅ Token renovado exitosamente');
      } catch (error) {
        console.error('❌ Error crítico en renovación de token:', error);

        if (
          error instanceof Error &&
          error.message.includes('REFRESH_TOKEN_INVALID')
        ) {
          console.error('🚨 ACCIÓN REQUERIDA: El refresh_token ha expirado.');
          console.error('📋 Pasos para solucionar:');
          console.error('   1. Ve a la consola de Zoho API');
          console.error('   2. Re-autoriza la aplicación');
          console.error('   3. Obtén nuevos tokens');
          console.error('   4. Actualiza el archivo tokens.json');

          throw new Error(
            'ZOHO_REAUTH_REQUIRED: Se requiere re-autenticación manual con Zoho. El refresh_token ha expirado.'
          );
        }

        throw error;
      }
    }

    if (!this.accessToken) {
      throw new Error(
        'No hay access_token disponible. Verifica la configuración de Zoho.'
      );
    }

    return this.accessToken;
  }

  public async getCalendars(): Promise<any> {
    const accessToken = await this.getAccessTokenZoho();

    try {
      const response = await axios.get(
        'https://calendar.zoho.com/api/v1/calendars',
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        }
      );
      console.log(response.data);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Error al obtener calendarios:', error.response?.data);
        throw error.response?.data;
      } else {
        console.error('Error:', error);
        throw error;
      }
    }
  }

  public async obtenerEventosDelCalendario(): Promise<any> {
    const accessToken = await this.getAccessTokenZoho();
    console.log('UID:', this.calendarUID);

    try {
      const response = await axios.get(
        `https://calendar.zoho.com/api/v1/calendars/${this.calendarUID}/events`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        }
      );
      console.log('Eventos del calendario:', response.data);
      return response.data;
    } catch (error) {
      console.log('Errroooooorr');
      if (axios.isAxiosError(error)) {
        console.error(
          'Error al obtener eventos del calendario:',
          error.response?.data
        );
        throw error.response?.data;
      } else {
        console.error('Error:', error);
        throw error;
      }
    }
  }

  public async createEvent(
    startDate: string,
    endDate: string,
    user: string
  ): Promise<{ status: boolean; eventData?: EventDetails; error?: any }> {
    const accessToken = await this.getAccessTokenZoho();
    const calendarId = this.calendarUID;

    const start =
      new Date(startDate).toISOString().replace(/[-:]/g, '').split('.')[0] +
      'Z';
    const end =
      new Date(endDate).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

    const eventData: EventData = {
      title: `Cita de ${user}`,
      dateandtime: {
        start: start,
        end: end,
        timezone: 'America/Mexico_City',
      },
      isallday: false,
      isprivate: false,
      location: 'C. Francisco I. Madero, Centro, 28000 Colima, Col.',
      description: 'Esto es una prueba :D',
      color: '#E574B0',
    };

    try {
      const response = await axios.post(
        `https://calendar.zoho.com/api/v1/calendars/${calendarId}/events?eventdata=${encodeURIComponent(
          JSON.stringify(eventData)
        )}`,
        {},
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      console.log('Evento creado:', response.data);
      return {
        status: true,
        eventData: response?.data?.events?.[0],
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Error al crear el evento:', error.response?.data);
        throw {
          status: false,
          error: error.response?.data,
        };
      } else {
        console.error('Error al crear el evento:', error);
        throw {
          status: false,
          error: error,
        };
      }
    }
  }

  public async updateEvent({
    eventUID,
    startDate,
    endDate,
    user,
    etag,
  }: {
    eventUID: string;
    startDate: string;
    endDate: string;
    user: string;
    etag: string;
  }): Promise<{ status: boolean; eventData?: EventDetails; error?: any }> {
    const accessToken = await this.getAccessTokenZoho();
    const calendarId = this.calendarUID;

    const start =
      new Date(startDate).toISOString().replace(/[-:]/g, '').split('.')[0] +
      'Z';
    const end =
      new Date(endDate).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

    const updatedEventData: any = {
      estatus: 'updated',
      uid: eventUID,
      title: `Cita de ${user}`,
      dateandtime: {
        start: start,
        end: end,
        timezone: 'America/Mexico_City',
      },
      location: 'C. Francisco I. Madero, Centro, 28000 Colima, Col.',
      description: 'Esto es una prueba modifación :D',
      color: '#E574B0',
      etag: etag,
    };

    try {
      const response = await axios.put(
        `https://calendar.zoho.com/api/v1/calendars/${calendarId}/events/${eventUID}?eventdata=${encodeURIComponent(
          JSON.stringify(updatedEventData)
        )}`,
        {},
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
            etag: etag,
          },
        }
      );

      return {
        status: true,
        eventData: response?.data?.events?.[0],
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Error al actualizar el evento:', error.response?.data);
        throw {
          status: false,
          error: error.response?.data,
        };
      } else {
        console.error('Error al actualizar el evento:', error);
        throw {
          status: false,
          error: error,
        };
      }
    }
  }

  public async getAppointments(
    dateAsString: string | Date,
    startTime: number,
    endTime: number
  ): Promise<ZohoAppointment[]> {
    const accessToken = await this.getAccessTokenZoho();
    const calendarId = this.calendarUID;

    const start = moment
      .tz(
        `${dateAsString} ${startTime}:00`,
        'YYYY-MM-DD H:mm',
        this.clientTimeZone
      )
      .utc()
      .format('YYYYMMDD[T]HHmmss[Z]');
    const end = moment
      .tz(
        `${dateAsString} ${endTime}:00`,
        'YYYY-MM-DD H:mm',
        this.clientTimeZone
      )
      .utc()
      .format('YYYYMMDD[T]HHmmss[Z]');
    const range = JSON.stringify({ start, end });

    try {
      const response = await axios.get(
        `https://calendar.zoho.com/api/v1/calendars/${calendarId}/events?range=${encodeURIComponent(
          range
        )}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        }
      );

      if (
        !response.data.events ||
        (response.data.events.length === 1 &&
          response.data.events[0]?.message === 'No events found.')
      ) {
        return [];
      }

      return response.data.events.map((event: any) => ({
        start: event.dateandtime?.start,
        end: event.dateandtime?.end,
        summary: event.title,
      }));
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw error.response?.data;
      } else {
        throw error;
      }
    }
  }

  public async getAvailableSlots(
    dateAsString: string,
    startTime: number,
    endTime: number
  ): Promise<Slot[]> {
    try {
      const bookedAppointments = await this.getAppointments(
        dateAsString,
        startTime,
        endTime
      );

      const slotDuration = 60; // Duración en minutos
      const timezone = 'America/Mexico_City';
      const availableSlots: Slot[] = [];
      let currentTime = startTime;

      while (currentTime < endTime) {
        const slotStartMoment = moment.tz(
          `${dateAsString} ${currentTime}:00`,
          'YYYY-MM-DD H:mm',
          timezone
        );
        const slotEndMoment = slotStartMoment
          .clone()
          .add(slotDuration, 'minutes');

        const slotStart = slotStartMoment.toISOString();
        const slotEnd = slotEndMoment.toISOString();

        const isSlotBooked = bookedAppointments.some(
          (appointment) =>
            (slotStart >= moment(appointment.start).toISOString() &&
              slotStart < moment(appointment.end).toISOString()) ||
            (slotEnd > moment(appointment.start).toISOString() &&
              slotEnd <= moment(appointment.end).toISOString())
        );

        if (!isSlotBooked) {
          availableSlots.push({
            start: slotStart,
            end: slotEnd,
          });
        }

        currentTime += slotDuration / 60;
      }

      return availableSlots;
    } catch (error) {
      console.log(error);
    }
  }

  public async isSlotAvailable(
    startDate: string | Date,
    endDate: string | Date
  ): Promise<boolean> {
    const accessToken = await this.getAccessTokenZoho();
    const calendarId = this.calendarUID;
    const timezone = 'America/Mexico_City';

    const slotStartMoment = moment.tz(startDate, timezone);
    const slotEndMoment = moment.tz(endDate, timezone);

    // Convert to the format expected by the API
    const slotStart = slotStartMoment.utc().format('YYYYMMDD[T]HHmmss[Z]');
    const slotEnd = slotEndMoment.utc().format('YYYYMMDD[T]HHmmss[Z]');

    const range = JSON.stringify({ start: slotStart, end: slotEnd });

    try {
      const response = await axios.get(
        `https://calendar.zoho.com/api/v1/calendars/${calendarId}/events?range=${encodeURIComponent(
          range
        )}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        }
      );

      const events = response.data.events;

      if (
        !events ||
        (events.length === 1 && events[0]?.message === 'No events found.')
      ) {
        return true; // Slot is available
      }

      // Check for overlapping events
      for (const event of events) {
        const eventStartMoment = moment(event.dateandtime.start);
        const eventEndMoment = moment(event.dateandtime.end);

        if (
          slotStartMoment.isBefore(eventEndMoment) &&
          slotEndMoment.isAfter(eventStartMoment)
        ) {
          return false; // Slot is not available
        }
      }

      return true; // Slot is available
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error(
          'Error checking slot availability:',
          error.response?.data
        );
        throw error.response?.data;
      } else {
        console.error('Error:', error);
        throw error;
      }
    }
  }

  public async deleteEvent(
    eventUID: string,
    etag?: string,
    eventData?: any
  ): Promise<any> {
    console.log({ eventUID, etag });
    const accessToken = await this.getAccessTokenZoho();

    let url = `https://calendar.zoho.com/api/v1/calendars/${this.calendarUID}/events/${eventUID}`;

    if (eventData) {
      url += `?eventdata=${encodeURIComponent(JSON.stringify(eventData))}`;
    }

    const headers: Record<string, string> = {
      Authorization: `Zoho-oauthtoken ${accessToken}`,
      'Content-Type': 'application/json',
    };

    if (etag) {
      headers['etag'] = etag;
    }

    try {
      const response = await axios.delete(url, { headers });
      console.log('Evento eliminado:', response.data);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Error al eliminar el evento:', error.response?.data);
        throw error.response?.data;
      } else {
        console.error('Error al eliminar el evento:', error);
        throw error;
      }
    }
  }

  public async getEventsByRange(start: Date, end: Date): Promise<any> {
    const tz = this.clientTimeZone; // e.g., 'America/Mexico_City'
    const startStr = moment.tz(start, tz).utc().format('YYYYMMDD[T]HHmmss[Z]');
    const endStr = moment.tz(end, tz).utc().format('YYYYMMDD[T]HHmmss[Z]');
    const range = JSON.stringify({ start: startStr, end: endStr });

    try {
      const accessToken = await this.getAccessTokenZoho();
      const response = await axios.get(
        `https://calendar.zoho.com/api/v1/calendars/${
          this.calendarUID
        }/events?range=${encodeURIComponent(range)}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        }
      );

      return response.data.events;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error(
          'Error al obtener eventos por rango:',
          error.response?.data
        );
        throw error.response?.data;
      } else {
        console.error('Error al obtener eventos por rango:', error);
        throw error;
      }
    }
  }
}

// TODO: colocar la logica singleton en el constructor como en la clase firebaseService.ts
const zohoInstance = ClassZohoApi.getInstance();
export default zohoInstance;
