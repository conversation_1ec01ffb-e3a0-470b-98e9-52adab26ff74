import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use a consistent path relative to the project root, not the current module location
// This ensures tokens.json is always in the same location regardless of source/compiled execution
const projectRoot = process.cwd();
const tokensFilePath = path.join(projectRoot, 'src', 'calendar', 'tokens.json');

interface TokensData {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export function saveTokens(
  accessToken: string,
  refreshToken: string,
  expiresIn: number
): void {
  const data: TokensData = {
    accessToken,
    refreshToken,
    expiresIn,
  };

  // Ensure the directory exists
  const tokensDir = path.dirname(tokensFilePath);
  if (!fs.existsSync(tokensDir)) {
    fs.mkdirSync(tokensDir, { recursive: true });
  }

  console.log('💾 Guardando tokens en:', tokensFilePath);
  fs.writeFileSync(tokensFilePath, JSON.stringify(data, null, 2));
  console.log('✅ Tokens guardados exitosamente');
}

export function readTokens(): TokensData | null {
  console.log('📖 Intentando leer tokens desde:', tokensFilePath);

  if (!fs.existsSync(tokensFilePath)) {
    console.log('❌ Archivo de tokens no encontrado en:', tokensFilePath);
    return null;
  }

  try {
    const data = JSON.parse(fs.readFileSync(tokensFilePath, 'utf8'));
    console.log('✅ Tokens leídos exitosamente');
    console.log('📋 Refresh token presente:', !!data.refreshToken);

    return {
      accessToken: data.accessToken,
      refreshToken: data.refreshToken,
      expiresIn: data.expiresIn,
    };
  } catch (error) {
    console.error('❌ Error leyendo tokens:', error);
    return null;
  }
}
