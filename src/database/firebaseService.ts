import moment from 'moment-timezone';
import { FirebaseDB } from './firebaseConfig';
import {
  Firestore,
  DocumentReference,
  DocumentData,
  FieldValue,
} from 'firebase-admin/firestore';
import { convertToISOString } from '~/utils/functions';

import { Appointment, AppointmentStates } from '~/model/AppointmentModel';
import { ClientInformation } from '~/model/CommonModel';

class FirebaseService {
  private static instance: FirebaseService;
  private db: Firestore;
  private collection: string | undefined;
  private docId: string | undefined;
  private companyRef: DocumentReference<DocumentData> | undefined;

  constructor() {
    if (!FirebaseService.instance) {
      this.db = FirebaseDB;
      this.collection = process.env.COLLECTION;
      this.docId = process.env.DOC_ID;
      this.companyRef =
        this.collection && this.docId
          ? this.db.collection(this.collection).doc(this.docId)
          : undefined;
      FirebaseService.instance = this;
    }
    return FirebaseService.instance;
  }

  /**
   * Checks if a user has an appointment.
   * @param phoneNumber - The phone number of the user.
   * @returns A promise that resolves to an object containing appointment status and data.
   *
   * TODO: Revisar que funcione
   */
  async userAppointment(phoneNumber: string): Promise<{
    hasAppointment: boolean | null;
    data: DocumentData | null;
    hasAppointmentExpired: boolean | null;
  }> {
    const result = {
      hasAppointment: null,
      data: null,
      hasAppointmentExpired: null,
    };

    try {
      const userAppointmentRef = this.companyRef
        .collection('citas_usuarios')
        .doc(phoneNumber.toString());

      const doc = await userAppointmentRef.get();
      const userCitaData = doc.data();

      if (userCitaData) {
        const horaFinCita = userCitaData.hora.split('-')[1].trim();
        const fechaHoraFinCita = convertToISOString(
          userCitaData.fecha,
          horaFinCita
        );

        console.log('horaFinCita: ', horaFinCita);
        console.log('fechaHoraFinCita:', fechaHoraFinCita);

        const mFechaHoraFinCita = moment.tz(
          fechaHoraFinCita,
          'America/Mexico_City'
        );
        const mFechaActual = moment.tz('America/Mexico_City');

        console.log('mFechaHoraFinCita: ', mFechaHoraFinCita);
        console.log('mFechaActual: ', mFechaActual);

        if (mFechaActual.isAfter(mFechaHoraFinCita)) {
          console.log('The appointment has expired.');
          result.hasAppointmentExpired = true;
        } else {
          console.log('The appointment is still valid.');
          result.hasAppointmentExpired = false;
        }
      }

      if (doc.exists) {
        console.log(
          `El usuario con número ${phoneNumber} ya tiene una cita agendada.`
        );
        result.hasAppointment = true;
        result.data = userCitaData;
      } else {
        console.log(
          `El usuario con número ${phoneNumber} no tiene ninguna cita agendada.`
        );
        result.hasAppointment = false;
      }

      return result;
    } catch (error) {
      console.error(
        `Error al verificar la cita para el usuario ${phoneNumber}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Adds an appointment to the database.
   * @param date - The date of the appointment (YYYY-MM-DD).
   * @param timeSlot - The time slot of the appointment.
   * @param appointmentData - The data of the appointment.
   * @returns A promise that resolves when the appointment is successfully added.
   */
  async addOrUpdateAppointment(
    date: string,
    timeSlot: string,
    appointmentData: Appointment
  ): Promise<Appointment> {
    console.log('addAppointment--------------');
    console.log({
      date,
      timeSlot,
      appointmentData,
    });
    try {
      // Step 2: Update the 'citas_usuarios' collection
      const userPhone = appointmentData.user;
      const userAppointmentRef = this.companyRef
        .collection('citas_usuarios')
        .doc(userPhone);

      const userAppointmentData: Appointment = {
        ...appointmentData,
        fecha: date,
        hora: timeSlot,
        nombre: appointmentData.nombre,
      };

      await userAppointmentRef.set(userAppointmentData, { merge: true });

      console.log('Cita agregada con éxito para el usuario', userPhone);
      return userAppointmentData;
    } catch (error) {
      console.error('Error al agregar cita:', error);
      throw error;
    }
  }

  /**
   * Deletes a user's appointment by their phone number.
   * @param phoneNumber - The phone number of the user.
   * @returns A promise that resolves to true if the deletion was successful.
   */
  async deleteUserAppointmentByPhone(phoneNumber: string): Promise<boolean> {
    if (!this.companyRef) {
      console.error('Company reference is undefined.');
      return false;
    }

    try {
      const userAppointmentRef = this.companyRef
        .collection('citas_usuarios')
        .doc(phoneNumber.toString());

      await userAppointmentRef.delete();

      console.log(
        `Documento de cita para el número ${phoneNumber} eliminado con éxito.`
      );
      return true;
    } catch (error) {
      console.error(
        `Error al eliminar el documento de cita para el número ${phoneNumber}:`,
        error
      );
      return false;
    }
  }

  /**
   * Actualiza (borrado lógico) la cita de un usuario, estableciendo su estado a 'Cancelada'.
   * @param phoneNumber - El número de teléfono del usuario.
   * @returns Una promesa que se resuelve en true si la actualización fue exitosa.
   */
  async cancelUserAppointmentByPhoneLogicalDeletion(
    phoneNumber: string,
    isRejected?: boolean
  ): Promise<boolean> {
    if (!this.companyRef) {
      console.error('Company reference is undefined.');
      return false;
    }

    try {
      const userAppointmentRef = this.companyRef
        .collection('citas_usuarios')
        .doc(phoneNumber.toString());

      // Se actualiza el documento, realizando un borrado lógico.
      await userAppointmentRef.update({
        status: isRejected
          ? AppointmentStates.Rechazada
          : AppointmentStates.Cancelada,
        cancelledAt: FieldValue.serverTimestamp(),
      });

      console.log(
        `Documento de cita para el número ${phoneNumber} actualizado a '${AppointmentStates.Cancelada}' con éxito.`
      );
      return true;
    } catch (error) {
      console.error(
        `Error al actualizar el documento de cita para el número ${phoneNumber}:`,
        error
      );
      return false;
    }
  }

  /**
   * Deletes a specific time slot from an appointment date.
   * @param date - The date of the appointment (YYYY-MM-DD).
   * @param time - The time slot to delete.
   * @returns A promise that resolves to true if the deletion was successful.
   */
  async deleteAppointmentTimeSlot(
    date: string,
    time: string
  ): Promise<boolean> {
    if (!this.companyRef) {
      console.error('Company reference is undefined.');
      return false;
    }

    try {
      // Reference to the document
      const docRef = this.companyRef.collection('citas_agendadas').doc(date);

      // Use the update function with FieldValue.delete() to remove a specific field from the document
      await docRef.update({
        [time]: FieldValue.delete(),
      });

      console.log(
        `La cita con hora ${time} eliminada con éxito del documento ${date}`
      );
      return true;
    } catch (error) {
      console.error(
        `Error al eliminar la cita con hora ${time} del documento ${date}:`,
        error
      );
      return false;
    }
  }

  /**
   * Acepta la cita actualizando el status
   * @param phoneNumber - El número de teléfono del usuario.
   * @returns Una promesa que se resuelve en true si la actualización fue exitosa.
   */
  async acceptAppointment(phoneNumber: string): Promise<boolean> {
    if (!this.companyRef) {
      console.error('Company reference is undefined.');
      return false;
    }

    try {
      const userAppointmentRef = this.companyRef
        .collection('citas_usuarios')
        .doc(phoneNumber.toString());

      // Se actualiza el documento, realizando un borrado lógico.
      await userAppointmentRef.update({
        status: AppointmentStates.Confirmada,
      });

      console.log(
        `Documento de cita para el número ${phoneNumber} actualizado a '${AppointmentStates.Confirmada}' con éxito.`
      );
      return true;
    } catch (error) {
      console.error(
        `Error al actualizar el documento de cita para el número ${phoneNumber}:`,
        error
      );
      return false;
    }
  }

  /**
   * Creates a new document in the  collection.
   * @param data - The data to add to the document.
   * @returns A promise that resolves to the document ID.
   */
  async createDocument(data: DocumentData): Promise<string> {
    try {
      const docRef: DocumentReference = await this.db
        .collection(this.collection as string)
        .add(data);
      return docRef.id;
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  }

  /**
   * Gets a specific document from Firestore.
   * @returns A promise that resolves to the document data.
   */
  async getDocument(): Promise<DocumentData | undefined> {
    try {
      const doc = await this.db
        .collection(this.collection as string)
        .doc(this.docId as string)
        .get();
      if (!doc.exists) {
        throw new Error('Document does not exist');
      }
      return doc.data();
    } catch (error) {
      console.error('Error retrieving document:', error);
      throw error;
    }
  }

  /**
   * Updates an existing document in Firestore.
   * @param data - The data to update in the document.
   * @returns A promise that resolves to true if the update was successful.
   */
  async updateDocument(data: Partial<DocumentData>): Promise<boolean> {
    try {
      await this.db
        .collection(this.collection as string)
        .doc(this.docId as string)
        .update(data);
      return true;
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  /**
   * Deletes a specific document from Firestore.
   * @returns A promise that resolves to true if the deletion was successful.
   */
  async deleteDocument(): Promise<boolean> {
    try {
      await this.db
        .collection(this.collection as string)
        .doc(this.docId as string)
        .delete();
      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  /**
   * Listens to changes in a specific document in Firestore.
   * @param callback - The callback to execute when changes occur.
   * @returns An Unsubscribe function to stop listening to changes.
   */
  listenToDocument(
    callback: (error: Error | null, data?: DocumentData) => void
  ) {
    return this.db
      .collection(this.collection as string)
      .doc(this.docId as string)
      .onSnapshot(
        (docSnapshot) => {
          if (docSnapshot.exists) {
            callback(null, docSnapshot.data());
          } else {
            callback(new Error('Document does not exist'));
          }
        },
        (error) => {
          console.error('Error listening to document:', error);
          callback(error);
        }
      );
  }

  /**
   * Sets the collection for Firestore actions.
   * @param newCollection - The new collection to set.
   */
  setCollection(newCollection: string): void {
    this.collection = newCollection;
  }

  /**
   * Sets the document ID for Firestore actions.
   * @param newDocId - The new document ID to set.
   */
  setDocId(newDocId: string): void {
    this.docId = newDocId;
  }

  /**
   * Invalida (marca como usados) todos los tokens de actualización de cita asociados al phoneNumber.
   */
  async invalidateOldAppointmentTokens(phoneNumber: string): Promise<void> {
    if (!this.companyRef) return;
    // TODO: ver como mejorar esto, ya que cada vez que invalida tiene que revisar todos los
    // tokens, no?
    const tokensRef = this.companyRef.collection('appointmentTokens');
    const snapshot = await tokensRef
      .where('phoneNumber', '==', phoneNumber)
      .get();
    const batch = this.db.batch();
    snapshot.forEach((doc) => {
      batch.update(doc.ref, { used: true });
    });
    await batch.commit();
  }

  /**
   * Agrega un token de actualización de cita usando un código corto.
   */
  async addAppointmentToken(
    shortCode: string,
    tokenData: {
      token: string;
      phoneNumber: string;
      appointmentId: string;
      newStartDate: string;
      newTime: string;
      createdAt: number;
      expiresAt: number;
      used: boolean;
    }
  ): Promise<void> {
    if (!this.companyRef) throw new Error('Company reference undefined');
    await this.companyRef
      .collection('appointmentTokens')
      .doc(shortCode)
      .set(tokenData);
  }

  /**
   * Recupera el documento del token de actualización usando el código corto.
   */
  async getAppointmentToken(shortCode: string): Promise<DocumentData | null> {
    if (!this.companyRef) return null;
    const docRef = this.companyRef
      .collection('appointmentTokens')
      .doc(shortCode);
    const doc = await docRef.get();
    return doc.exists ? doc.data() : null;
  }

  /**
   * Marca como usado el token identificado por el código corto.
   */
  async markAppointmentTokenUsed(shortCode: string): Promise<void> {
    if (!this.companyRef) throw new Error('Company reference undefined');
    await this.companyRef
      .collection('appointmentTokens')
      .doc(shortCode)
      .update({ used: true });
  }

  /**
   * Actualiza el estado de una cita en la colección 'citas_usuarios'.
   * @param appointmentId - El ID del documento de la cita (por ejemplo, el número de teléfono del usuario).
   * @param newStatus - El nuevo estado a asignar a la cita.
   * @returns Una promesa que se resuelve en true si la actualización fue exitosa, false en caso contrario.
   */
  async updateAppointmentStatus(
    appointmentId: string,
    newStatus: AppointmentStates
  ): Promise<boolean> {
    if (!this.companyRef) {
      console.error('Company reference is undefined.');
      return false;
    }
    try {
      const userAppointmentRef = this.companyRef
        .collection('citas_usuarios')
        .doc(appointmentId);
      await userAppointmentRef.update({ status: newStatus });
      console.log(
        `La cita con ID ${appointmentId} se actualizó a: ${newStatus}`
      );
      return true;
    } catch (error) {
      console.error(
        `Error al actualizar la cita con ID ${appointmentId}: `,
        error
      );
      return false;
    }
  }

  async userExistsOrAdd(userData: ClientInformation): Promise<boolean> {
    if (!userData?.phoneNumber) {
      console.error(
        'El teléfono es requerido para verificar o agregar un usuario'
      );
      return false;
    }

    const userRef = this.companyRef
      .collection('clients')
      .doc(userData.phoneNumber);

    try {
      // create() intentará crear el documento y fallará si ya existe.
      await userRef.create(userData);
      console.log('Usuario agregado con éxito');
      return false; // El usuario no existía y se creó
    } catch (error: any) {
      // Si el error indica que el documento ya existe, retornamos true.
      if (
        error.code === 6 || // Code 6 = ALREADY_EXISTS
        error.message.includes('already exists')
      ) {
        console.log(
          `El usuario con teléfono ${userData.phoneNumber} ya existe`
        );
        return true;
      }
      console.error('Error al verificar o agregar el usuario:', error);
      throw error;
    }
  }

  async getClients(): Promise<ClientInformation[]> {
    if (!this.companyRef) {
      console.error('Company reference is undefined.');
      return [];
    }
    try {
      const snapshot = await this.companyRef.collection('clients').get();
      const users: ClientInformation[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        users.push({
          id: doc.id,
          name: data.name,
          ngrams: data.ngrams,
          phoneNumber: data.phoneNumber,
        });
      });
      return users;
    } catch (error) {
      console.error('Error al obtener usuarios:', error);
      throw error;
    }
  }
}

const firebaseServiceInstance = new FirebaseService();
export default firebaseServiceInstance;
