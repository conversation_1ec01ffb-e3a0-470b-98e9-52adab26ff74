import admin from 'firebase-admin';
import { validateFirebaseEnv } from '../config/envValidation';

// Validar variables de entorno Firebase antes de inicializar
const firebaseEnv = validateFirebaseEnv();

// Firebase Admin SDK configuration using validated environment variables
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: firebaseEnv.FIREBASE_PROJECT_ID,
      clientEmail: firebaseEnv.FIREBASE_CLIENT_EMAIL,
      privateKey: firebaseEnv.FIREBASE_PRIVATE_KEY,
    }),
  });
  console.log('🔥 Firebase connected successfully!');
}

const FirebaseDB = admin.firestore();

export { FirebaseDB, admin };
