#!/usr/bin/env tsx

/**
 * Script interactivo para re-autenticación con Zoho Calendar
 *
 * Este script te guía paso a paso para obtener nuevos tokens de Zoho
 * cuando los tokens existentes han expirado o son inválidos.
 *
 * Uso: npx tsx src/scripts/reauth-zoho.ts
 */

import dotenv from 'dotenv';
import path from 'path';
import * as readline from 'readline';

// Cargar variables de entorno desde el archivo .env en la raíz del proyecto
const envPath = path.resolve(process.cwd(), '.env');
console.log('📁 Cargando variables de entorno desde:', envPath);
dotenv.config({ path: envPath });

// Importar después de cargar las variables de entorno
const { default: zohoInstance } = await import('../calendar/ClassZohoApi.js');

// Configurar readline para entrada interactiva
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function reAuthZoho() {
  console.log('🔐 RE-AUTENTICACIÓN CON ZOHO CALENDAR');
  console.log('====================================\n');

  try {
    // Verificar variables de entorno
    console.log('📋 VERIFICANDO CONFIGURACIÓN:');
    const clientId = process.env.ZOHO_CLIENT_ID;
    const clientSecret = process.env.ZOHO_CLIENT_SECRET;
    const redirectUrl = process.env.ZOHO_REDIRECT_URL;
    const calendarUid = process.env.ZOHO_CALENDAR_UID;

    if (!clientId) {
      console.error('❌ ZOHO_CLIENT_ID no está configurado en .env');
      return;
    }
    if (!clientSecret) {
      console.error('❌ ZOHO_CLIENT_SECRET no está configurado en .env');
      return;
    }
    if (!redirectUrl) {
      console.error('❌ ZOHO_REDIRECT_URL no está configurado en .env');
      return;
    }
    if (!calendarUid) {
      console.error('❌ ZOHO_CALENDAR_UID no está configurado en .env');
      return;
    }

    console.log('✅ Todas las variables de entorno están configuradas\n');

    // Generar URL de autorización
    const authUrl = `https://accounts.zoho.com/oauth/v2/auth?scope=ZohoCalendar.calendar.ALL,ZohoCalendar.event.ALL&client_id=${clientId}&response_type=code&redirect_uri=${encodeURIComponent(
      redirectUrl
    )}&access_type=offline`;

    console.log('🌐 PASO 1: OBTENER CÓDIGO DE AUTORIZACIÓN');
    console.log('==========================================');
    console.log('');
    console.log('1. Abre la siguiente URL en tu navegador:');
    console.log('');
    console.log('🔗', authUrl);
    console.log('');
    console.log('2. Inicia sesión en tu cuenta de Zoho si es necesario');
    console.log('3. Autoriza la aplicación para acceder a tu calendario');
    console.log(
      '   (Scope requerido: ZohoCalendar.calendar.ALL,ZohoCalendar.event.ALL)'
    );
    console.log(
      '4. Serás redirigido a una URL que contiene el código de autorización'
    );
    console.log(
      '5. Copia el valor del parámetro "code" de la URL de redirección'
    );
    console.log('');
    console.log('Ejemplo de URL de redirección:');
    console.log(
      `${redirectUrl}?code=1000.abc123def456...&location=us&accounts-server=https%3A%2F%2Faccounts.zoho.com`
    );
    console.log('');
    console.log('En este ejemplo, el código sería: 1000.abc123def456...');
    console.log('');

    // Solicitar el código al usuario
    const authCode = await askQuestion(
      '📝 Ingresa el código de autorización obtenido: '
    );

    if (!authCode) {
      console.error('❌ No se proporcionó ningún código de autorización');
      return;
    }

    console.log('\n🔄 PASO 2: OBTENIENDO NUEVOS TOKENS');
    console.log('===================================');

    try {
      console.log('📤 Enviando código a Zoho para obtener tokens...');
      const accessToken = await zohoInstance.firstConnectionWithAPIZoho(
        authCode
      );

      console.log('✅ ¡Tokens obtenidos exitosamente!');
      console.log('📋 Access Token obtenido:', accessToken ? 'Sí' : 'No');
      console.log('');

      // Verificar el estado de los nuevos tokens
      console.log('🔍 PASO 3: VERIFICANDO NUEVOS TOKENS');
      console.log('====================================');

      const tokenStatus = zohoInstance.getTokenStatus();
      console.log(
        'Access Token presente:',
        tokenStatus.hasAccessToken ? '✅' : '❌'
      );
      console.log(
        'Refresh Token presente:',
        tokenStatus.hasRefreshToken ? '✅' : '❌'
      );
      console.log(
        'Token expirado:',
        tokenStatus.isAccessTokenExpired ? '❌' : '✅'
      );
      console.log('Expira en:', tokenStatus.timeUntilExpiration);
      console.log('Fecha de expiración:', tokenStatus.accessTokenExpiresAt);
      console.log('');

      // Probar la conexión
      console.log('🧪 PASO 4: PROBANDO CONEXIÓN');
      console.log('============================');

      try {
        const calendars = await zohoInstance.getCalendars();
        console.log('✅ ¡Conexión exitosa con Zoho Calendar!');
        console.log(
          '📅 Calendarios encontrados:',
          calendars?.calendars?.length || 0
        );
        console.log('');
        console.log('🎉 RE-AUTENTICACIÓN COMPLETADA EXITOSAMENTE');
        console.log('===========================================');
        console.log('');
        console.log(
          'Los nuevos tokens han sido guardados y la conexión está funcionando.'
        );
        console.log(
          'Tu aplicación ahora puede acceder a Zoho Calendar nuevamente.'
        );
      } catch (testError) {
        console.error('❌ Error al probar la conexión:', testError);
        console.log('');
        console.log(
          '⚠️  Los tokens se obtuvieron pero hay un problema con la conexión.'
        );
        console.log('Verifica tu configuración y vuelve a intentar.');
      }
    } catch (tokenError) {
      console.error('❌ Error al obtener tokens:', tokenError);
      console.log('');
      console.log('🔧 POSIBLES SOLUCIONES:');
      console.log('- Verifica que el código de autorización sea correcto');
      console.log(
        '- Asegúrate de que el código no haya expirado (son válidos por poco tiempo)'
      );
      console.log(
        '- Verifica que la ZOHO_REDIRECT_URL coincida exactamente con la configurada en Zoho'
      );
      console.log('- Intenta generar un nuevo código de autorización');
    }
  } catch (error) {
    console.error('❌ Error en el proceso de re-autenticación:', error);
  } finally {
    rl.close();
  }
}

// Ejecutar re-autenticación
reAuthZoho().catch(console.error);
