#!/usr/bin/env tsx

/**
 * Script de diagnóstico para verificar el estado de los tokens de Zoho
 *
 * Uso: npx tsx src/scripts/diagnose-zoho-tokens.ts
 */

import dotenv from 'dotenv';
import path from 'path';

// Cargar variables de entorno desde el archivo .env en la raíz del proyecto
const envPath = path.resolve(process.cwd(), '.env');
console.log('📁 Cargando variables de entorno desde:', envPath);
dotenv.config({ path: envPath });

// Importar después de cargar las variables de entorno
const { default: zohoInstance } = await import('../calendar/ClassZohoApi.js');

async function diagnoseZohoTokens() {
  console.log('🔍 DIAGNÓSTICO DE TOKENS DE ZOHO');
  console.log('================================\n');

  try {
    // Verificar variables de entorno
    console.log('📋 VARIABLES DE ENTORNO:');
    console.log(
      'ZOHO_CLIENT_ID:',
      process.env.ZOHO_CLIENT_ID ? '✅ Configurado' : '❌ No configurado'
    );
    console.log(
      'ZOHO_CLIENT_SECRET:',
      process.env.ZOHO_CLIENT_SECRET ? '✅ Configurado' : '❌ No configurado'
    );
    console.log(
      'ZOHO_REDIRECT_URL:',
      process.env.ZOHO_REDIRECT_URL || '❌ No configurado'
    );
    console.log(
      'ZOHO_CALENDAR_UID:',
      process.env.ZOHO_CALENDAR_UID ? '✅ Configurado' : '❌ No configurado'
    );
    console.log('');

    // Verificar estado de tokens
    console.log('🔑 ESTADO DE TOKENS:');
    const tokenStatus = zohoInstance.getTokenStatus();

    console.log(
      'Access Token:',
      tokenStatus.hasAccessToken ? '✅ Presente' : '❌ Ausente'
    );
    console.log(
      'Refresh Token:',
      tokenStatus.hasRefreshToken ? '✅ Presente' : '❌ Ausente'
    );
    console.log(
      'Access Token Expirado:',
      tokenStatus.isAccessTokenExpired ? '❌ Sí' : '✅ No'
    );
    console.log('Expira en:', tokenStatus.timeUntilExpiration);
    console.log('Fecha de expiración:', tokenStatus.accessTokenExpiresAt);
    console.log('');

    // Intentar renovar token si está expirado
    if (tokenStatus.isAccessTokenExpired && tokenStatus.hasRefreshToken) {
      console.log('🔄 INTENTANDO RENOVAR TOKEN...');
      try {
        await zohoInstance.getAccessTokenZoho();
        console.log('✅ Token renovado exitosamente');

        // Verificar nuevo estado
        const newTokenStatus = zohoInstance.getTokenStatus();
        console.log(
          'Nuevo estado - Expira en:',
          newTokenStatus.timeUntilExpiration
        );
        console.log(
          'Nueva fecha de expiración:',
          newTokenStatus.accessTokenExpiresAt
        );
      } catch (error) {
        console.error('❌ Error al renovar token:', error);

        if (
          error instanceof Error &&
          error.message.includes('REFRESH_TOKEN_INVALID')
        ) {
          console.log('\n🚨 SOLUCIÓN REQUERIDA:');
          console.log(
            'El refresh_token ha expirado. Necesitas re-autorizar la aplicación:'
          );
          console.log('');
          console.log('1. Ve a: https://api-console.zoho.com/');
          console.log('2. Selecciona tu aplicación');
          console.log('3. Ve a "Generate Code" o "Self Client"');
          console.log(
            '4. Usa el scope: ZohoCalendar.calendar.ALL,ZohoCalendar.event.ALL'
          );
          console.log('5. Genera un nuevo código de autorización');
          console.log('6. Usa el código para obtener nuevos tokens');
          console.log('');
          console.log('URL de autorización:');
          const authUrl = `https://accounts.zoho.com/oauth/v2/auth?scope=ZohoCalendar.calendar.ALL,ZohoCalendar.event.ALL&client_id=${
            process.env.ZOHO_CLIENT_ID
          }&response_type=code&redirect_uri=${encodeURIComponent(
            process.env.ZOHO_REDIRECT_URL || ''
          )}&access_type=offline`;
          console.log(authUrl);
        }
      }
    }

    // Intentar hacer una llamada de prueba después de la renovación
    console.log('\n🧪 PROBANDO CONEXIÓN CON ZOHO API...');
    try {
      const calendars = await zohoInstance.getCalendars();
      console.log('✅ Conexión exitosa con Zoho API');
      console.log(
        '📅 Calendarios encontrados:',
        calendars?.calendars?.length || 0
      );
    } catch (error) {
      console.error('❌ Error al conectar con Zoho API:', error);
    }
  } catch (error) {
    console.error('❌ Error en diagnóstico:', error);
  }
}

// Ejecutar diagnóstico
diagnoseZohoTokens().catch(console.error);
