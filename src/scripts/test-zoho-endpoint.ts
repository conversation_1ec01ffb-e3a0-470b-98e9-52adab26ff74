#!/usr/bin/env tsx

/**
 * Script para probar el endpoint de eventos de Zoho
 * 
 * Uso: npx tsx src/scripts/test-zoho-endpoint.ts
 */

import dotenv from 'dotenv';
import path from 'path';

// Cargar variables de entorno
const envPath = path.resolve(process.cwd(), '.env');
console.log('📁 Cargando variables de entorno desde:', envPath);
dotenv.config({ path: envPath });

async function testZohoEndpoint() {
  console.log('🧪 PROBANDO ENDPOINT DE EVENTOS DE ZOHO');
  console.log('=====================================\n');

  const PORT = process.env.PORT || 3008;
  const baseUrl = `http://localhost:${PORT}`;
  
  try {
    // Verificar que el servidor esté corriendo
    console.log('🔍 Verificando que el servidor esté corriendo...');
    
    // Crear fechas de prueba (próximos 7 días)
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + 7);
    
    const startISO = startDate.toISOString();
    const endISO = endDate.toISOString();
    
    console.log('📅 Fechas de prueba:');
    console.log('  Inicio:', startISO);
    console.log('  Fin:', endISO);
    
    const url = `${baseUrl}/v1/events?start=${encodeURIComponent(startISO)}&end=${encodeURIComponent(endISO)}`;
    console.log('\n📤 URL de prueba:', url);
    
    console.log('\n🔄 Realizando petición...');
    
    const response = await fetch(url);
    
    console.log('📊 Status:', response.status);
    console.log('📊 Status Text:', response.statusText);
    
    const responseText = await response.text();
    
    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log('✅ Respuesta exitosa:');
        console.log('📋 Eventos encontrados:', data.data?.events?.length || 0);
        
        if (data.data?.events?.length > 0) {
          console.log('📅 Primer evento:', {
            title: data.data.events[0].title || 'Sin título',
            start: data.data.events[0].start || 'Sin fecha',
            end: data.data.events[0].end || 'Sin fecha'
          });
        }
      } catch (parseError) {
        console.log('✅ Respuesta exitosa (texto plano):', responseText);
      }
    } else {
      console.error('❌ Error en la respuesta:');
      console.error('📋 Contenido:', responseText);
      
      try {
        const errorData = JSON.parse(responseText);
        console.error('📋 Error estructurado:', errorData);
      } catch (parseError) {
        console.error('📋 Error como texto plano:', responseText);
      }
    }
    
  } catch (error) {
    console.error('❌ Error al probar el endpoint:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        console.error('🚨 El servidor no está corriendo. Inicia el servidor con:');
        console.error('   npm run dev');
      } else if (error.message.includes('fetch')) {
        console.error('🚨 Error de red. Verifica que el servidor esté accesible.');
      }
    }
  }
}

// Ejecutar prueba
testZohoEndpoint().catch(console.error);
