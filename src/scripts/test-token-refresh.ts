#!/usr/bin/env tsx

/**
 * Script para probar el mecanismo de renovación de tokens
 * Fuerza la expiración del token y verifica que se renueve correctamente
 * 
 * Uso: npx tsx src/scripts/test-token-refresh.ts
 */

import dotenv from 'dotenv';
import path from 'path';
import { saveTokens, readTokens } from '../calendar/manageKeys.js';

// Cargar variables de entorno
const envPath = path.resolve(process.cwd(), '.env');
console.log('📁 Cargando variables de entorno desde:', envPath);
dotenv.config({ path: envPath });

// Importar después de cargar las variables de entorno
const { default: zohoInstance } = await import('../calendar/ClassZohoApi.js');

async function testTokenRefresh() {
  console.log('🧪 PROBANDO MECANISMO DE RENOVACIÓN DE TOKENS');
  console.log('=============================================\n');

  try {
    // 1. Verificar estado inicial
    console.log('📋 ESTADO INICIAL:');
    const initialStatus = zohoInstance.getTokenStatus();
    console.log('Access Token:', initialStatus.hasAccessToken ? '✅ Presente' : '❌ Ausente');
    console.log('Refresh Token:', initialStatus.hasRefreshToken ? '✅ Presente' : '❌ Ausente');
    console.log('Expirado:', initialStatus.isAccessTokenExpired ? '❌ Sí' : '✅ No');
    console.log('Expira en:', initialStatus.timeUntilExpiration);
    console.log('');

    // 2. Leer tokens actuales
    const currentTokens = readTokens();
    if (!currentTokens) {
      console.error('❌ No se pudieron leer los tokens actuales');
      return;
    }

    console.log('💾 FORZANDO EXPIRACIÓN DEL TOKEN...');
    // Forzar expiración estableciendo una fecha en el pasado
    const expiredTime = Date.now() - 1000; // 1 segundo en el pasado
    saveTokens(currentTokens.accessToken, currentTokens.refreshToken, expiredTime);
    console.log('✅ Token marcado como expirado');
    console.log('');

    // 3. Crear nueva instancia para que cargue los tokens expirados
    console.log('🔄 CREANDO NUEVA INSTANCIA DE ZOHO API...');
    // Necesitamos forzar la recarga de tokens
    const { default: newZohoInstance } = await import('../calendar/ClassZohoApi.js?t=' + Date.now());
    
    // 4. Verificar que el token está expirado
    const expiredStatus = newZohoInstance.getTokenStatus();
    console.log('📋 ESTADO DESPUÉS DE FORZAR EXPIRACIÓN:');
    console.log('Access Token:', expiredStatus.hasAccessToken ? '✅ Presente' : '❌ Ausente');
    console.log('Refresh Token:', expiredStatus.hasRefreshToken ? '✅ Presente' : '❌ Ausente');
    console.log('Expirado:', expiredStatus.isAccessTokenExpired ? '❌ Sí' : '✅ No');
    console.log('Expira en:', expiredStatus.timeUntilExpiration);
    console.log('');

    // 5. Intentar usar la API (esto debería activar la renovación automática)
    console.log('🔄 PROBANDO RENOVACIÓN AUTOMÁTICA...');
    console.log('Intentando obtener calendarios (esto debería renovar el token automáticamente)...');
    
    const calendars = await newZohoInstance.getCalendars();
    console.log('✅ Renovación automática exitosa!');
    console.log('📅 Calendarios obtenidos:', calendars?.calendars?.length || 0);
    console.log('');

    // 6. Verificar estado final
    const finalStatus = newZohoInstance.getTokenStatus();
    console.log('📋 ESTADO FINAL:');
    console.log('Access Token:', finalStatus.hasAccessToken ? '✅ Presente' : '❌ Ausente');
    console.log('Refresh Token:', finalStatus.hasRefreshToken ? '✅ Presente' : '❌ Ausente');
    console.log('Expirado:', finalStatus.isAccessTokenExpired ? '❌ Sí' : '✅ No');
    console.log('Expira en:', finalStatus.timeUntilExpiration);
    console.log('Nueva fecha de expiración:', finalStatus.accessTokenExpiresAt);
    console.log('');

    // 7. Verificar que el archivo se actualizó
    const updatedTokens = readTokens();
    if (updatedTokens) {
      console.log('💾 VERIFICACIÓN DEL ARCHIVO:');
      console.log('Tokens guardados correctamente:', updatedTokens.expiresIn > Date.now() ? '✅ Sí' : '❌ No');
      console.log('Nueva expiración en archivo:', new Date(updatedTokens.expiresIn).toISOString());
    }

    console.log('\n🎉 PRUEBA COMPLETADA EXITOSAMENTE');
    console.log('El mecanismo de renovación automática está funcionando correctamente.');

  } catch (error) {
    console.error('❌ Error durante la prueba:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('REFRESH_TOKEN_INVALID')) {
        console.error('\n🚨 El refresh_token ha expirado.');
        console.error('Necesitas re-autorizar la aplicación manualmente.');
      } else if (error.message.includes('No hay refresh_token disponible')) {
        console.error('\n🚨 No se encontró refresh_token.');
        console.error('Verifica que el archivo tokens.json contenga un refresh_token válido.');
      }
    }
  }
}

// Ejecutar prueba
testTokenRefresh().catch(console.error);
