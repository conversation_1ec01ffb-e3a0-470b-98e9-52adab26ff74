import { WeekDay } from '~/model/CommonModel';
import { EnglishMonth, SpanishMonth } from '../model/AppointmentModel';

export const numberToEmoji = {
  1: '1️⃣',
  2: '2️⃣',
  3: '3️⃣',
  4: '4️⃣',
  5: '5️⃣',
  6: '6️⃣',
  7: '7️⃣',
  8: '8️⃣',
  9: '9️⃣',
  10: '🔟',
  11: '1️⃣1️⃣',
  12: '1️⃣2️⃣',
  13: '1️⃣3️⃣',
  14: '1️⃣4️⃣',
  15: '1️⃣5️⃣',
  16: '1️⃣6️⃣',
  17: '1️⃣7️⃣',
  18: '1️⃣8️⃣',
  19: '1️⃣9️⃣',
  20: '2️⃣0️⃣',
};

export const dayMap = {
  lunes: 0,
  martes: 1,
  miercoles: 2,
  miércoles: 2,
  jueves: 3,
  viernes: 4,
  sabado: 5,
  sábado: 5,
  domingo: 6,
};

export const monthMap: { [key: string]: number } = {
  Enero: 1,
  Febrero: 2,
  <PERSON><PERSON>: 3,
  <PERSON>bri<PERSON>: 4,
  Mayo: 5,
  <PERSON><PERSON>: 6,
  Julio: 7,
  Agosto: 8,
  Septiembre: 9,
  Octubre: 10,
  <PERSON><PERSON>bre: 11,
  Diciembre: 12,
};

export const hour24Conversion: { [key: string]: number } = {
  '8:00am': 8,
  '9:00am': 9,
  '10:00am': 10,
  '11:00am': 11,
  '12:00pm': 12,
  '1:00pm': 13,
  '2:00pm': 14,
  '3:00pm': 15,
  '4:00pm': 16,
  '5:00pm': 17,
  '6:00pm': 18,
  '7:00pm': 19,
  '8:00pm': 20,
  '9:00pm': 21,
  '10:00pm': 22,
};

export const spanishToEnglishMonths: Record<SpanishMonth, EnglishMonth> = {
  Enero: 'January',
  Febrero: 'February',
  Marzo: 'March',
  Abril: 'April',
  Mayo: 'May',
  Junio: 'June',
  Julio: 'July',
  Agosto: 'August',
  Septiembre: 'September',
  Octubre: 'October',
  Noviembre: 'November',
  Diciembre: 'December',
};

export const WEEK_DAYS: WeekDay[] = [
  WeekDay.Monday,
  WeekDay.Tuesday,
  WeekDay.Wednesday,
  WeekDay.Thursday,
  WeekDay.Friday,
  WeekDay.Saturday,
  WeekDay.Sunday,
];

export const WEEK_DAYS_MAP: { [key: string]: WeekDay } = {
  lunes: WeekDay.Monday,
  martes: WeekDay.Tuesday,
  miércoles: WeekDay.Wednesday,
  jueves: WeekDay.Thursday,
  viernes: WeekDay.Friday,
  sábado: WeekDay.Saturday,
  domingo: WeekDay.Sunday,
};
