import moment from 'moment-timezone';
import {
  format,
  addMonths,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isAfter,
} from 'date-fns';
import { es } from 'date-fns/locale';
import { numberToEmoji, spanishToEnglishMonths } from './consts';
import { MonthYear } from '~/model/FunctionsModel';
import { EventEntry } from '../model/AppointmentModel';
import { TimeRange } from '~/model/CompanyModel';
import { ZohoAppointment, Slot } from '~/model/ZohoCalendarModel';
import zohoInstance from '~/calendar/ClassZohoApi';

/**
 * Returns a random number between 200 and 500.
 * This can be used as a delay in milliseconds for sending messages.
 * @returns A random number between 200 and 500.
 */
export const getRandomDelay = (): number => {
  const min = 200;
  const max = 500;
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Converts a date and hour to ISO string format.
 * @param dateString - The date as a string in 'YYYY-MM-DD' format.
 * @param hour - The hour as a string or number.
 * @returns A string representing the date and time in ISO format.
 */
export function convertToISOString(
  dateString: string,
  hour: string | number
): string {
  // Asegurarse de que la hora tiene un formato de dos dígitos (08 en lugar de 8)
  const formattedHour = String(hour).padStart(2, '0');

  // Construye la fecha con la hora
  const dateTime = moment.tz(
    `${dateString} ${formattedHour}:00:00`,
    'YYYY-MM-DD HH:mm:ss',
    'America/Mexico_City'
  );

  return dateTime.format();
}

/**
 * Formats a Zoho date string to 'YYYY-MM-DDTHH:mm:ssZ' format in the 'America/Mexico_City' timezone.
 * @param zohoDate - The date string from Zoho in 'YYYYMMDDTHHmmssZ' format.
 * @returns A formatted date string in 'YYYY-MM-DDTHH:mm:ssZ' format.
 */
export function formatZohoDate(zohoDate: string): string {
  return moment(zohoDate, 'YYYYMMDDTHHmmssZ')
    .tz('America/Mexico_City')
    .format('YYYY-MM-DDTHH:mm:ssZ');
}

/**
 * Returns an array of objects containing month names and years, starting from the current month and
 * including the next three consecutive months.
 * @returns An array of objects with 'month' and 'year' properties.
 */
export const getMonthsToDisplay = (): MonthYear[] => {
  const currentDate = new Date();
  const months: MonthYear[] = [];
  const MONTHS_TO_SWOW = 3;

  for (let i = 0; i < MONTHS_TO_SWOW; i++) {
    const newMonth = addMonths(currentDate, i);
    const monthName = format(newMonth, 'MMMM', { locale: es });
    // Capitaliza la primera letra y concatena el resto de la palabra
    const month = monthName.charAt(0).toUpperCase() + monthName.slice(1);
    const year = newMonth.getFullYear();

    months.push({
      month: month,
      year: year,
    });
  }

  return months;
};
/**
 * Returns the weeks and their corresponding days for a given month,
 * excluding the current date and showing only future dates.
 * @param year - The year of the month to get weeks for.
 * @param month - The index of the month (0 for January, 11 for December).
 * @returns An object containing the month name and the weeks with their corresponding days.
 */
export const getWeeksOfMonth = (year: number, month: number) => {
  const currentDate = new Date();
  const startDate = startOfMonth(new Date(year, month));
  const endDate = endOfMonth(new Date(year, month));

  const allDays = eachDayOfInterval({ start: startDate, end: endDate });

  const weeks: { [key: number]: string[] } = {};
  let weekNumber = 1;

  allDays.forEach((date) => {
    // Validamos que solo se incluyan fechas posteriores a la fecha actual
    if (isAfter(date, currentDate)) {
      if (!weeks[weekNumber]) {
        weeks[weekNumber] = [];
      }

      const dayWithNumber = format(date, 'EEEE dd', { locale: es });

      // Agregar el día al formato 'Día DD'
      const formattedDay =
        dayWithNumber.charAt(0).toUpperCase() +
        dayWithNumber.slice(1, dayWithNumber.length);
      weeks[weekNumber].push(formattedDay);

      // Si el día es domingo, avanzamos a la siguiente semana
      if (format(date, 'EEEE', { locale: es }) === 'domingo') {
        weekNumber++;
      }
    }
  });

  return {
    monthName:
      format(startDate, 'MMMM', { locale: es }).charAt(0).toUpperCase() +
      format(startDate, 'MMMM', { locale: es }).slice(1),
    weeks,
  };
};

/**
 * Converts the weeks object into an array of formatted strings for each week.
 * @param weeks - The weeks object returned from getWeeksOfMonth.
 * @returns An array of formatted strings representing the start and end of each week.
 */
export const getFormattedWeeks = (weeks: {
  [key: number]: string[];
}): string[] => {
  const formattedWeeks: string[] = [];

  Object.keys(weeks).forEach((weekNumber) => {
    const days = weeks[parseInt(weekNumber)];

    // Si la semana tiene un solo día
    if (days.length === 1) {
      formattedWeeks.push(`${numberToEmoji[weekNumber]} -> ${days[0]}`);
    } else {
      const firstDay = days[0]; // Primer día de la semana
      const lastDay = days[days.length - 1]; // Último día de la semana
      formattedWeeks.push(
        `${numberToEmoji[weekNumber]} -> ${firstDay} - ${lastDay}`
      );
    }
  });

  return formattedWeeks;
};

export function extractHours(datesArray: EventEntry[]): string[] {
  return datesArray.map((entry) => {
    const date = new Date(entry.start); // Crear objeto Date a partir de la cadena 'start'

    // Opciones para formatear la hora
    const options: Intl.DateTimeFormatOptions = {
      hour: 'numeric', // Incluir la hora
      minute: '2-digit', // Incluir los minutos con dos dígitos
      hour12: true, // Formato de 12 horas (AM/PM)
      timeZone: 'America/Mexico_City', // Especificar zona horaria
    };

    // Formatear la hora según las opciones
    const formattedTime = date
      .toLocaleTimeString('en-US', options) // Obtener la hora formateada
      .toLowerCase() // Convertir a minúsculas
      .replace(' ', '') // Eliminar espacios adicionales
      .replace(':00', ':00'); // Asegurar que los minutos se muestren correctamente

    return formattedTime; // Retornar la hora formateada
  });
}

export function convertDateToISO8601(
  day: string,
  month: string,
  year: number,
  time: string
): string {
  if (!day || !month || !year || !time) {
    console.error('One or more parameters are missing or undefined!');
    console.log('day:', day);
    console.log('month:', month);
    console.log('year:', year);
    console.log('time:', time);
    return 'Invalid input';
  }

  // Extract day number from "Lunes 31" or "Miércoles 09"
  const dayParts = day.split(' ');
  if (dayParts.length < 2) {
    console.error('Day string format is invalid.');
    return 'Invalid day format';
  }

  const dayNumber = parseInt(dayParts[1], 10);
  if (isNaN(dayNumber) || dayNumber < 1 || dayNumber > 31) {
    console.error('Day number is invalid.');
    return 'Invalid day number';
  }

  // Convert Spanish month name to English
  const englishMonth = spanishToEnglishMonths[month];
  if (!englishMonth) {
    console.error('Month name is invalid or not in Spanish.');
    return 'Invalid month name';
  }

  // Convert time from "5:00pm" to "17:00"
  const timeMoment = moment(time, ['h:mma', 'H:mm']);
  if (!timeMoment.isValid()) {
    console.error('Time format is invalid.');
    return 'Invalid time format';
  }
  const time24Format = timeMoment.format('HH:mm');

  // Create the date object with moment
  const dateString = `${dayNumber} ${englishMonth} ${year} ${time24Format}`;
  const date = moment.tz(
    dateString,
    'D MMMM YYYY HH:mm',
    'America/Mexico_City'
  );

  if (!date.isValid()) {
    console.error('Constructed date is invalid.');
    return 'Invalid date';
  }

  // Return in ISO8601 format
  return date.format('YYYY-MM-DDTHH:mm:ssZ');
}

/**
 * Adds one hour to an ISO8601 date string.
 * @param isoDate - The ISO8601 date string to which one hour will be added.
 * @returns The new ISO8601 date string after adding one hour.
 */
export function addOneHourToISODate(isoDate: string): string {
  if (!isoDate) {
    console.error('isoDate parameter is missing or undefined!');
    return 'Invalid input';
  }

  // Parse the ISO date string in the specified timezone
  const date = moment.tz(isoDate, moment.ISO_8601, 'America/Mexico_City');

  // Validate the parsed date
  if (!date.isValid()) {
    console.error('Invalid ISO date format.');
    return 'Invalid date';
  }

  // Add one hour
  const newDate = date.add(1, 'hours');

  // Return the new date in ISO8601 format with timezone offset
  return newDate.format('YYYY-MM-DDTHH:mm:ssZ');
}

export function formatDateForMexicoCity(inputDate: string | Date): string {
  const date = moment.tz(inputDate, 'America/Mexico_City').locale('es');

  return date.format('dddd, D [de] MMMM [de] YYYY, [a las] h:mm a');
}

export function generateNgrams(input: string): string[] {
  // Se elimina acentos y diacríticos: convierte "á" en "a", etc.
  const normalizedInput = input
    .trim()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');
  const ngramsSet = new Set<string>();

  // Genera n-grams acumulativos para el input completo
  let currentCombination = '';
  for (const char of normalizedInput) {
    if (char !== ' ') {
      currentCombination += char.toLowerCase();
      ngramsSet.add(currentCombination);
    } else {
      currentCombination += ' ';
    }
  }

  // Genera n-grams para cada palabra de forma individual
  const words = normalizedInput.split(' ').filter((word) => word.length > 0);
  for (const word of words) {
    let curr = '';
    for (const char of word) {
      curr += char.toLowerCase();
      ngramsSet.add(curr);
    }
  }

  return Array.from(ngramsSet);
}

/**
 * Obtiene de forma optimizada los slots disponibles para un día determinado.
 *
 * La función realiza una sola llamada para obtener todas las citas (appointments)
 * en un rango que abarca desde el inicio más temprano hasta el final más tardío de los intervalos de trabajo.
 * Luego, genera los posibles slots de 1 hora para cada intervalo y descarta aquellos que colisionan con una cita.
 *
 * @param workTime - Configuración de horarios de trabajo del día.
 * @param dateAsString - Fecha en formato 'YYYY-MM-DD'.
 * @returns Una promesa que resuelve en un arreglo de slots disponibles.
 */
export async function getFilteredAvailableSlotsForDay(
  workTime: TimeRange[],
  dateAsString: string | Date
): Promise<Slot[]> {
  const availableSlots: Slot[] = [];
  const timezone = 'America/Mexico_City';
  const slotDurationMinutes = 60;

  // Determinar el rango global que abarca todos los intervalos de trabajo.
  const startTimes = workTime.map((interval) => interval.start);
  const endTimes = workTime.map((interval) => interval.end);
  const globalStart = Math.min(...startTimes);
  const globalEnd = Math.max(...endTimes);

  // Realizamos una sola llamada para obtener todas las citas en el rango global.
  const bookedAppointments: ZohoAppointment[] =
    await zohoInstance.getAppointments(dateAsString, globalStart, globalEnd);

  // Procesamos cada intervalo de trabajo definido.
  for (const interval of workTime) {
    let currentHour = interval.start;
    while (currentHour < interval.end) {
      const slotStartMoment = moment.tz(
        `${dateAsString} ${currentHour}:00`,
        'YYYY-MM-DD H:mm',
        timezone
      );
      const slotEndMoment = slotStartMoment
        .clone()
        .add(slotDurationMinutes, 'minutes');
      const slotStart = slotStartMoment.toISOString();
      const slotEnd = slotEndMoment.toISOString();

      // Verificar si el slot se superpone con alguna cita reservada.
      const isSlotBooked = bookedAppointments.some((appointment) => {
        const appStart = moment(appointment.start).toISOString();
        const appEnd = moment(appointment.end).toISOString();
        return (
          (slotStart >= appStart && slotStart < appEnd) ||
          (slotEnd > appStart && slotEnd <= appEnd)
        );
      });

      if (!isSlotBooked) {
        availableSlots.push({ start: slotStart, end: slotEnd });
      }

      // Avanzar 1 hora (se asume que los horarios son en horas completas).
      currentHour += 1;
    }
  }

  // Ordenar los slots por hora de inicio.
  availableSlots.sort(
    (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
  );
  return availableSlots;
}
