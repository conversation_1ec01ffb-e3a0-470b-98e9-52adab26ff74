import { createStore } from 'zustand/vanilla';
import { CompanyData } from '~/model/CompanyModel';

interface CompanyState {
  loading: boolean;
  error: string | null;
  companyData: CompanyData | null;
}

interface CompanyActions {
  updateCompanyData: (companyData: CompanyData) => void;
}

const initialState: CompanyState = {
  loading: false,
  error: null,
  companyData: null,
};

export const useCompanyStore = createStore<CompanyState & CompanyActions>(
  (set) => ({
    ...initialState,
    updateCompanyData: (companyData: CompanyData) => {
      set({ loading: true, error: null });
      try {
        console.log('Actualizando datos de la compañía en el store...');

        set({ companyData });
        console.log('Datos actualizados correctamente!');
      } catch (error: any) {
        set({ error: error.message || 'Error al actualizar la compañía' });
      } finally {
        set({ loading: false });
      }
    },
  })
);
