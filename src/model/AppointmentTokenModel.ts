export enum TokenValidationCode {
  MISSING_CODE = 0,
  VALID,
  TOKEN_EXPIRED,
  TOKEN_INVALID,
  TOKEN_ALREADY_USED,
}

export enum TokenValidationMessages {
  MISSING_CODE = "Faltan parámetros: se requiere 'code'.",
  TOKEN_EXPIRED = 'El código ha expirado.',
  TOKEN_INVALID = 'El código es inválido.',
  TOKEN_ALREADY_USED = 'El código ya ha sido utilizado.',
}

export enum AppointmentUpdateMessages {
  CODE_INVALID = 'El código es inválido o ha expirado.',
  CODE_ALREADY_USED = 'El código ha sido utilizado o ha expirado.',
  APPOINTMENT_CONFIRMED = 'La cita se ha confirmado exitosamente.',
  APPOINTMENT_REJECTED = 'La cita se ha rechazado exitosamente.',
  INTERNAL_SERVER_ERROR = 'Error interno del servidor al actualizar la cita.',
  MISSING_PARAMETERS = 'Parámetros faltantes: se requiere token y acción.',
}

export type TokenActionType = 'accept' | 'reject';

export interface TokenValidationRequestQuery {
  token: string;
}

export interface TokenValidationResponse {
  valid: boolean;
  message: string;
  tokenCode: TokenValidationCode;
  appointmentData?: {
    appointmentId: string;
    phoneNumber: string;
    newStartDate: string;
    newTime: string;
  };
}
