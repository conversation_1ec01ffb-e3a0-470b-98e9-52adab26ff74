import { Timestamp } from 'firebase-admin/firestore';
import { TokenActionType } from './AppointmentTokenModel';

export interface DeleteAppointmentParams {
  eventUid: string;
  etag: string;
  phone: string;
  isRejected?: boolean;
}

export enum AppointmentStates {
  Confirmada = 'Confirmada',
  Aceptada = 'Aceptada',
  Cancelada = 'Cancelada',
  Pendiente = 'Pendiente',
  Rechazada = 'Rechazada',
}

export type TimeRange = {
  start: number;
  end: number;
  id: string;
};

export type TimeRangeSelectorProps = {
  startHour?: number;
  endHour?: number;
  onRangesChange: (ranges: TimeRange[]) => void;
  value?: TimeRange[];
};

export enum MonthsOfYear {
  Enero = 0,
  Febrero = 1,
  Marzo = 2,
  Abril = 3,
  Mayo = 4,
  Junio = 5,
  Julio = 6,
  Agosto = 7,
  Septiembre = 8,
  Octubre = 9,
  Noviembre = 10,
  Diciembre = 11,
}

export interface EventEntry {
  start: string;
  end: string;
}

export type SpanishMonth =
  | 'Enero'
  | 'Febrero'
  | 'Marzo'
  | 'Abril'
  | 'Mayo'
  | 'Junio'
  | 'Julio'
  | 'Agosto'
  | 'Septiembre'
  | 'Octubre'
  | 'Noviembre'
  | 'Diciembre';

export type EnglishMonth =
  | 'January'
  | 'February'
  | 'March'
  | 'April'
  | 'May'
  | 'June'
  | 'July'
  | 'August'
  | 'September'
  | 'October'
  | 'November'
  | 'December';

export interface Appointment {
  start: string;
  end: string;
  user: string;
  nombre: string;
  fecha?: string; // fecha y hora se llenan en addOrUpdateAppointment
  hora?: string;
  ngrams: string[];
  timestampDate: Timestamp;
  eventUid: string;
  viewEventURL: string;
  etag: string;
  status: AppointmentStates;
  cancelledAt: Timestamp | null;
  tokenCode?: string;
  tokenExpiresAt?: number;
  confirmationUrl?: string;
}

export interface ConfirmAppointmentUpdateRequestBody {
  token: string;
  action: TokenActionType;
  eventUid: string;
  etag: string;
  phoneNumber: string;
}

export interface ConfirmAppointmentUpdateResponse {
  message: string;
}
