import { Timestamp } from 'firebase-admin/firestore';
import { WeekDay } from './CommonModel';

export interface TimeRange {
  id: string;
  start: number;
  end: number;
}

export interface DaySchedule {
  dayName: string;
  isAvailable: boolean;
  workTime: TimeRange[];
}

export interface CompanyData {
  companyName: string;
  address: string;
  workDays: Record<WeekDay, DaySchedule>;
  daysOff: Timestamp[];
}
