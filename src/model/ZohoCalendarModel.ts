export interface EventData {
  title: string;
  dateandtime: {
    start: string;
    end: string;
    timezone: string;
  };
  isallday: boolean;
  isprivate: boolean;
  location: string;
  description: string;
  color: string;
}

export interface ZohoAppointment {
  start: string;
  end: string;
  summary: string;
}

export interface Slot {
  start: string;
  end: string;
}

export interface EventDetails {
  enable_eventmanagement: boolean;
  role: 'organizer' | 'participant' | 'viewer';
  color: string;
  orgDName: string;
  calid: string;
  description: string;
  has_attachment: boolean;
  title: string;
  calendar_alarm: boolean;
  duration: number; // Duración en milisegundos
  uid: string;
  createdby: string;
  etype: 'EVENT' | 'MEETING' | 'APPOINTMENT';
  id: string;
  isApproved: boolean;
  viewEventURL: string;
  dateandtime: {
    timezone: string;
    start: string; // Formato ISO 8601
    end: string; // Formato ISO 8601
  };
  lastmodifiedtime: string; // Formato ISO 8601
  isprivate: boolean;
  rsvpStatus: number;
  createdtime_millis: number;
  createdtime: string; // Formato ISO 8601
  user_timezone: string;
  notifyType: number;
  estatus: 'added' | 'modified' | 'deleted';
  organizer: string;
  isallday: boolean;
  transparency: number;
  etag: string;
  location: string;
  modifiedby: string;
  caluid: string;
  multiday: boolean;
  allowForwarding: boolean;
  attendees?: Array<{
    email: string;
    status: 'NEEDS-ACTION' | 'ACCEPTED' | 'DECLINED' | 'TENTATIVE';
  }>;
  reminders?: Array<{
    action: 'email' | 'popup' | 'notification';
    minutes: number;
  }>;
  recurrence?: {
    freq: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
    interval?: number;
    count?: number;
    until?: string; // Formato ISO 8601
    byday?: string;
    bymonthday?: number;
    bymonth?: number;
  };
}
