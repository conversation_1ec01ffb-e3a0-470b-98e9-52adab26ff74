import { ProviderClass } from '@builderbot/bot';
import {
  BotStateGlobal,
  BotStateStandAlone,
  DispatchFn,
  DynamicBlacklist,
} from '@builderbot/bot/dist/types';
import { IncomingMessage, ServerResponse } from 'http';

export type BotType = Pick<ProviderClass, 'sendMessage' | 'vendor'> & {
  provider: ProviderClass;
  blacklist: DynamicBlacklist;
  dispatch: DispatchFn;
  state: (number: string) => BotStateStandAlone;
  globalState: () => BotStateGlobal;
};

export interface RequestType<T = any, Q = any> extends IncomingMessage {
  body: T;
  query?: Q;
}

export type ResponseType = ServerResponse;

export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500,
}

export enum ReponseStatusCode {
  Success = 0,
  MissingData,
  AppointmentAlreadyCancelled,
  ServerError,
  BadRequest,
  Unauthorized,
}

export interface SuccessResponse<T = unknown> {
  statusCode: ReponseStatusCode;
  data?: T;
}

export interface ErrorResponse<T = unknown> {
  message: string;
  statusCode: ReponseStatusCode;
  data?: T;
}
