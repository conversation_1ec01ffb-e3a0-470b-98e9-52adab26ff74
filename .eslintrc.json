{"env": {"browser": true, "node": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:builderbot/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "builderbot"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/ban-types": "off", "no-unsafe-optional-chaining": "off"}}