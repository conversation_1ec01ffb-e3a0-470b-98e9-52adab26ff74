# 🤖 Botsito Backend

**WhatsApp Bot para Gestión de Citas** - Backend Node.js con BuilderBot y Firebase

## 📋 Descripción

Botsito es un backend de WhatsApp bot diseñado para gestionar citas de manera automatizada. Utiliza BuilderBot para la integración con WhatsApp Business API y Firebase Firestore como base de datos.

### ✨ Características Principales

- 🔥 **Firebase Firestore** - Base de datos en tiempo real
- 📱 **WhatsApp Business API** - Integración con BuilderBot
- 📅 **Zoho Calendar** - Integración opcional para sincronización
- 🔒 **Validación robusta** - Variables de entorno con Zod
- 🚀 **Fácil despliegue** - Configuración con variables de entorno
- 📊 **API REST** - Endpoints para gestión de citas

## 🚀 Inicio Rápido

### 1. Prerrequisitos

- Node.js 18+
- Cuenta Firebase con Firestore habilitado
- WhatsApp Business Account (opcional para desarrollo)

### 2. Instalación

```bash
# Clonar el repositorio
git clone <repository-url>
cd botsito_be

# Instalar dependencias
npm install

# Configurar variables de entorno
npm run setup
```

### 3. Configuración de Variables de Entorno

El comando `npm run setup` creará un archivo `.env` desde `.env.example`. Necesitas configurar:

#### 🔥 Variables Firebase (REQUERIDAS)

```env
FIREBASE_PROJECT_ID=tu-proyecto-firebase
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nTU_CLAVE_PRIVADA\n-----END PRIVATE KEY-----\n"
```

#### 🗂️ Variables Firestore (REQUERIDAS)

```env
COLLECTION=companies
DOC_ID=tu-documento-id
```

#### ⚙️ Variables Opcionales

```env
PORT=3008
ZOHO_CLIENT_ID=tu-zoho-client-id
ZOHO_CLIENT_SECRET=tu-zoho-secret
ZOHO_REDIRECT_URL=http://localhost:3000/oauth2callback
ZOHO_CALENDAR_UID=tu-calendar-uid
```

### 4. Obtener Credenciales Firebase

1. Ve a [Firebase Console](https://console.firebase.google.com)
2. Selecciona tu proyecto o crea uno nuevo
3. Ve a **Project Settings > Service Accounts**
4. Click en **"Generate new private key"**
5. Descarga el archivo JSON y extrae los valores para las variables de entorno

### 5. Ejecutar la Aplicación

```bash
# Desarrollo
npm run dev

# Producción
npm run build
npm start

# Verificar Firebase
npm run test:firebase
```

## 📚 Scripts Disponibles

| Comando                 | Descripción                                       |
| ----------------------- | ------------------------------------------------- |
| `npm run dev`           | Ejecuta en modo desarrollo con hot reload         |
| `npm run build`         | Compila el proyecto para producción               |
| `npm start`             | Ejecuta la aplicación compilada                   |
| `npm run setup`         | Configura variables de entorno desde .env.example |
| `npm run test:firebase` | Verifica conectividad con Firebase                |
| `npm run lint`          | Ejecuta ESLint                                    |

## 🔧 Arquitectura

```
src/
├── app.ts                 # Punto de entrada principal
├── config/
│   ├── envConfig.ts       # Configuración de variables de entorno
│   └── envValidation.ts   # Validación con Zod
├── database/
│   ├── firebaseConfig.ts  # Configuración Firebase
│   └── firebaseService.ts # Servicios de Firestore
├── flows/                 # Flujos de conversación del bot
├── controllers/           # Controladores de API REST
├── model/                 # Modelos de datos TypeScript
└── utils/                 # Utilidades y helpers
```

## 🌐 API Endpoints

### Gestión de Citas

- `POST /v1/appointments/cancel` - Cancelar cita
- `POST /v1/appointments/delete` - Eliminar cita
- `POST /v1/appointments/accept` - Aceptar cita
- `POST /v1/appointments/update` - Actualizar cita
- `GET /v1/appointments/available-hours` - Horas disponibles
- `GET /v1/appointments/validate-date` - Validar fecha

### Gestión de Mensajes

- `POST /v1/message` - Enviar mensaje
- `POST /v1/blacklist` - Gestionar lista negra

### Utilidades

- `GET /v1/qr` - Obtener código QR de WhatsApp
- `GET /v1/events` - Obtener eventos del calendario

## 🔒 Seguridad

- ✅ **Variables de entorno** - Credenciales no hardcodeadas
- ✅ **Validación robusta** - Zod para validación de tipos
- ✅ **Firebase Admin SDK** - Autenticación segura
- ✅ **Gitignore configurado** - Archivos sensibles excluidos

## 🚀 Despliegue

### Railway/Render

1. Conecta tu repositorio
2. Configura las variables de entorno en el panel
3. Despliega automáticamente

### Docker

```bash
# Construir imagen
docker build -t botsito-backend .

# Ejecutar contenedor
docker run -p 3008:3008 --env-file .env botsito-backend
```

## 🛠️ Desarrollo

### Estructura de Datos Firebase

```
companies/{DOC_ID}/
├── appointments/          # Citas agendadas
├── clients/              # Información de clientes
├── users/                # Usuarios del sistema
└── settings/             # Configuraciones
```

### Agregar Nuevos Flujos

1. Crear archivo en `src/flows/`
2. Importar en `src/flows/index.ts`
3. Configurar palabras clave y respuestas

## 📋 TODO

- [ ] Crear método de validación completa para agendar citas
- [ ] Mejorar manejo de estados de citas (aceptadas/canceladas)
- [ ] Implementar Cloud Function para limpiar citas canceladas
- [ ] Agregar indicador "Escribiendo..."
- [ ] Implementar caché para consultas frecuentes
- [ ] Revisar flujo completo de agendamiento
- [ ] Agregar mas tiempo de delay a los mensaje (aumentar el tiempo en el helper)
- [ ] Usar un numero de telefono en whats por 6 meses

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.
