# BuilderBot Framework - Referencia Completa

## Información General

**BuilderBot** es un framework gratuito y de código abierto para crear chatbots y aplicaciones inteligentes que se conectan a diferentes canales de comunicación como WhatsApp, Telegram y otros. Ganador del primer premio en OpenExpo 2024 🏆.

### Características Principales
- Framework intuitivo y extensible
- Soporte para múltiples proveedores de WhatsApp
- Arquitectura modular con componentes intercambiables
- Soporte para TypeScript y JavaScript
- Integración con IA (OpenAI, Gemini, Langchain)
- Documentación completa y comunidad activa

### Instalación Rápida
```bash
pnpm create builderbot@latest
# o
npm create builderbot@latest
```

## Arquitectura Core

BuilderBot se basa en tres componentes clave:

### 1. Flow (Flujo de Conversación)
- **Propósito**: Crear secuencias estructuradas de interacciones
- **Métodos principales**: `addKeyword()` y `addAnswer()`
- **Funcionalidad**: Asociar palabras clave con respuestas específicas

### 2. Provider (Proveedor de Comunicación)
- **Propósito**: Actuar como conector entre el bot y el canal de comunicación
- **Ventaja**: Cambiar fácilmente entre proveedores sin afectar la lógica del bot
- **Proveedores disponibles**: Meta, Twilio, Baileys, Venom, WPPConnect, Telegram

### 3. Database (Base de Datos)
- **Propósito**: Proporcionar persistencia de datos y registro de eventos
- **Adaptadores**: Mongo, MySQL, Postgres, MemoryDB
- **Funcionalidad**: Mantener contexto entre conversaciones

## Funciones Principales

### addKeyword()
Punto de inicio de un flujo de conversación.

```typescript
// Palabra clave única
addKeyword('hello').addAnswer('¡Hola! Bienvenido')

// Múltiples palabras clave
addKeyword(['hello', 'hi', 'hola']).addAnswer('¡Bienvenido!')

// Con regex
const REGEX_EMAIL = /(\w+)@gmail\.com/g;
addKeyword(REGEX_EMAIL, { regex: true })
    .addAnswer('Gracias por tu email de Gmail')

// Sensible a mayúsculas/minúsculas
addKeyword('BUY', { sensitive: true })
    .addAnswer('Procesando compra...')
```

### addAnswer()
Envía mensajes al usuario.

```typescript
// Mensaje simple
.addAnswer('Hola, ¿cómo puedo ayudarte?')

// Mensajes consecutivos
.addAnswer('Hola')
.addAnswer('Bienvenido a mi tienda')
.addAnswer('¿En qué puedo ayudarte?')

// Mensaje con saltos de línea
.addAnswer([
    'Hola',
    'Bienvenido a mi tienda',
    '¿En qué puedo ayudarte?'
])

// Mensaje con delay
.addAnswer('Este mensaje aparecerá después de 2 segundos', 
    { delay: 2000 })

// Mensaje con callback
.addAnswer('¿Sabes cuánto es 4+4?', null, async (_, {flowDynamic}) => {
    const sum = 4 + 4
    await flowDynamic(`Total: ${sum}`)
})

// Enviar multimedia
.addAnswer('Aquí tienes una imagen', 
    { media: 'https://i.imgur.com/0HpzsEm.png' })
```

### addAction()
Define acciones específicas como respuesta a mensajes.

```typescript
// Acción simple
.addAction(async (_, { flowDynamic }) => {
    console.log('El usuario quiere comprar algo')
    return flowDynamic('¿Qué quieres comprar?')
})

// Acción con captura
.addAction({ capture: true }, async (ctx, { flowDynamic, state }) => {
    await state.update({ name: ctx.body })
    return flowDynamic(`Dijiste: ${ctx.body}`)
})
```

## Proveedores Disponibles

### 1. Meta (WhatsApp Business API)
```typescript
import { MetaProvider } from '@builderbot/provider-meta'

const adapterProvider = createProvider(MetaProvider, {
    jwtToken: 'tu_jwt_token',
    numberId: 'tu_number_id',
    verifyToken: 'tu_verify_token',
    version: 'v16.0',
})
```

### 2. Twilio
```typescript
import { TwilioProvider } from '@builderbot/provider-twilio'

const adapterProvider = createProvider(TwilioProvider, {
    accountSid: 'YOUR_ACCOUNT_SID',
    authToken: 'YOUR_ACCOUNT_TOKEN',
    vendorNumber: '+***********',
    publicUrl: "public_url", // opcional
})
```

### 3. Baileys (Gratuito - Requiere QR)
```typescript
import { BaileysProvider } from '@builderbot/provider-baileys'

const adapterProvider = createProvider(BaileysProvider)
```

### 4. Venom (Gratuito - Requiere QR)
```typescript
import { VenomProvider } from '@builderbot/provider-venom'

const adapterProvider = createProvider(VenomProvider)
```

### 5. WPPConnect (Gratuito - Requiere QR)
```typescript
import { WPPConnectProvider } from '@builderbot/provider-wppconnect'

const adapterProvider = createProvider(WPPConnectProvider)
```

## Bases de Datos

### MemoryDB (Desarrollo)
```typescript
import { MemoryDB } from '@builderbot/bot'
const adapterDB = new MemoryDB()
```

### MongoDB
```typescript
import { MongoAdapter } from '@builderbot/database-mongo'
const adapterDB = new MongoAdapter({
    dbUri: 'mongodb://localhost:27017',
    dbName: 'builderbot'
})
```

### MySQL
```typescript
import { MySQLAdapter } from '@builderbot/database-mysql'
const adapterDB = new MySQLAdapter({
    host: 'localhost',
    user: 'root',
    database: 'builderbot',
    password: ''
})
```

## Ejemplo Completo

```typescript
import { createBot, createProvider, createFlow, addKeyword, MemoryDB } from '@builderbot/bot'
import { BaileysProvider } from '@builderbot/provider-baileys'

const welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hello', 'hi'])
    .addAnswer('¡Hola! Bienvenido')
    .addAnswer('Envío imagen desde URL', { 
        media: 'https://i.imgur.com/0HpzsEm.png' 
    })

const main = async () => {
    const adapterDB = new MemoryDB()
    const adapterFlow = createFlow([welcomeFlow])
    const adapterProvider = createProvider(BaileysProvider)

    const { handleCtx, httpServer } = await createBot({
        flow: adapterFlow,
        provider: adapterProvider,
        database: adapterDB,
    })

    httpServer(3000)

    // API REST endpoint
    adapterProvider.server.post('/v1/messages', handleCtx(async (bot, req, res) => {
        const { number, message } = req.body
        await bot.sendMessage(number, message, {})
        return res.end('sent')
    }))
}

main()
```

## Características Avanzadas

### Plugins Disponibles
- **Telegram**: Soporte para Telegram
- **Shopify**: Integración con Shopify
- **Agents**: Agentes inteligentes
- **Langchain**: Integración con Langchain

### Funcionalidades Especiales
- **Queue limit**: Límite de cola de mensajes
- **Modularización**: Organización de flujos en módulos
- **Fast Entries**: Entradas rápidas
- **Idle**: Manejo de inactividad
- **Scheduled Reboots**: Reinicios programados
- **Event handling**: Manejo de eventos de entrada/salida
- **Reminders**: Recordatorios con cron
- **Forward to human**: Transferencia a humanos
- **GotoFlow**: Navegación entre flujos
- **Multiple messages**: Mensajes múltiples

### Despliegue
- **Railway**: Despliegue en Railway
- **Docker**: Containerización
- **VPS**: Servidor privado virtual

## Recursos y Comunidad

- **Sitio web**: https://www.builderbot.app
- **GitHub**: https://github.com/codigoencasa/builderbot
- **Discord**: Comunidad activa
- **Curso oficial**: Disponible para aprender todas las funcionalidades
- **Documentación completa**: Guías, tutoriales y ejemplos

## Migración desde bot-whatsapp

BuilderBot es la evolución de bot-whatsapp, con mejor arquitectura y más funcionalidades. Existe documentación específica para migrar proyectos existentes.

## Licencia

MIT License - Proyecto de código abierto
