# 🔄 Guía de Migración - Firebase Credentials

Esta guía documenta la migración de credenciales hardcodeadas de Firebase a variables de entorno.

## 📋 Resumen de Cambios

### ✅ Antes (Inseguro)
- Credenciales Firebase en `firebaseKeys.json`
- Archivo JSON con credenciales en el repositorio
- Riesgo de exposición de credenciales sensibles

### ✅ Después (Seguro)
- Credenciales en variables de entorno (`.env`)
- Validación robusta con Zod
- Archivo `.env` excluido del repositorio
- Scripts de configuración automatizados

## 🔧 Cambios Implementados

### 1. Estructura de Archivos

#### Archivos Eliminados
- `firebaseKeys.json` ❌ (eliminado por seguridad)

#### Archivos Nuevos
- `src/config/envValidation.ts` ✅ (validación con Zod)
- `.env.example` ✅ (plantilla de configuración)
- `scripts/setup-env.js` ✅ (configuración automatizada)
- `scripts/test-firebase.js` ✅ (tests de conectividad)
- `docs/MIGRATION_GUIDE.md` ✅ (esta guía)

#### Archivos Modificados
- `src/database/firebaseConfig.ts` 🔄 (usa variables de entorno)
- `src/config/envConfig.ts` 🔄 (validación integrada)
- `package.json` 🔄 (nuevos scripts)
- `README.md` 🔄 (documentación actualizada)

### 2. Variables de Entorno

#### Variables Firebase (REQUERIDAS)
```env
FIREBASE_PROJECT_ID=tu-proyecto-firebase
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nTU_CLAVE_PRIVADA\n-----END PRIVATE KEY-----\n"
```

#### Variables Aplicación (REQUERIDAS)
```env
COLLECTION=companies
DOC_ID=tu-documento-id
```

#### Variables Opcionales
```env
PORT=3008
ZOHO_CLIENT_ID=tu-zoho-client-id
ZOHO_CLIENT_SECRET=tu-zoho-secret
ZOHO_REDIRECT_URL=http://localhost:3000/oauth2callback
ZOHO_CALENDAR_UID=tu-calendar-uid
```

### 3. Nuevos Scripts NPM

| Script | Comando | Descripción |
|--------|---------|-------------|
| Setup | `npm run setup` | Configura variables de entorno desde .env.example |
| Test Firebase | `npm run test:firebase` | Verifica conectividad con Firebase |

## 🚀 Migración para Desarrolladores

### Si ya tienes el proyecto configurado:

1. **Verificar que tienes las variables de entorno:**
   ```bash
   npm run setup
   ```

2. **Verificar conectividad Firebase:**
   ```bash
   npm run test:firebase
   ```

3. **Compilar y ejecutar:**
   ```bash
   npm run build
   npm start
   ```

### Para nuevas instalaciones:

1. **Clonar y configurar:**
   ```bash
   git clone <repository-url>
   cd botsito_be
   npm install
   npm run setup
   ```

2. **Configurar variables en .env:**
   - Editar el archivo `.env` creado
   - Agregar credenciales Firebase reales
   - Configurar variables de Firestore

3. **Verificar configuración:**
   ```bash
   npm run test:firebase
   ```

## 🔒 Beneficios de Seguridad

### ✅ Antes vs Después

| Aspecto | Antes | Después |
|---------|-------|---------|
| **Credenciales** | Hardcodeadas en JSON | Variables de entorno |
| **Repositorio** | Credenciales expuestas | Credenciales protegidas |
| **Validación** | Sin validación | Validación robusta con Zod |
| **Configuración** | Manual y propensa a errores | Automatizada con scripts |
| **Testing** | Sin tests de conectividad | Tests automatizados |
| **Documentación** | Mínima | Completa y actualizada |

### 🛡️ Medidas de Seguridad Implementadas

1. **Variables de entorno** - Credenciales no hardcodeadas
2. **Validación con Zod** - Tipos y formatos verificados
3. **Gitignore actualizado** - `.env` excluido del repositorio
4. **Backup seguro** - Credenciales respaldadas localmente
5. **Tests automatizados** - Verificación de conectividad
6. **Documentación completa** - Guías paso a paso

## 🔧 Troubleshooting

### Error: Variables de entorno faltantes
```bash
❌ Variables de entorno faltantes: FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL
```

**Solución:**
1. Ejecutar `npm run setup`
2. Editar `.env` con credenciales reales
3. Verificar con `npm run test:firebase`

### Error: Firebase connection failed
```bash
❌ Error conectando a Firebase: Invalid private key
```

**Solución:**
1. Verificar que `FIREBASE_PRIVATE_KEY` incluye `\n` para saltos de línea
2. Verificar que está entre comillas dobles
3. Regenerar credenciales en Firebase Console si es necesario

### Error: Build failed
```bash
❌ firebaseKeys.json not found
```

**Solución:**
- Este error indica que el código aún referencia el archivo antiguo
- Verificar que no hay imports de `firebaseKeys.json` en el código
- Ejecutar `npm run build` después de la migración

## 📚 Referencias

- [Firebase Admin SDK Setup](https://firebase.google.com/docs/admin/setup)
- [Environment Variables Best Practices](https://12factor.net/config)
- [Zod Validation Library](https://zod.dev/)
- [BuilderBot Documentation](https://builderbot.vercel.app/)

## 🎯 Próximos Pasos

1. **Monitoreo** - Implementar logging de errores de autenticación
2. **Rotación** - Establecer proceso de rotación de credenciales
3. **Auditoría** - Revisar accesos y permisos periódicamente
4. **Backup** - Automatizar backup de configuraciones críticas

---

**Fecha de migración:** 2025-01-04  
**Versión:** 1.0.0  
**Estado:** ✅ Completada
