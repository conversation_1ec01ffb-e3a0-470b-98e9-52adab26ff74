# =============================================================================
# BOTSITO BACKEND - CONFIGURACIÓN DE VARIABLES DE ENTORNO
# =============================================================================
# 
# Este archivo contiene todas las variables de entorno necesarias para 
# ejecutar el backend de Botsito. Copia este archivo como .env y 
# configura los valores apropiados.
#
# IMPORTANTE: Nunca subas el archivo .env al repositorio
# =============================================================================

# -----------------------------------------------------------------------------
# CONFIGURACIÓN DE FIREBASE ADMIN SDK (REQUERIDO)
# -----------------------------------------------------------------------------
# Estas credenciales se obtienen del service account de Firebase
# Ve a: Firebase Console > Project Settings > Service Accounts > Generate new private key

# ID del proyecto Firebase
FIREBASE_PROJECT_ID=tu-proyecto-firebase

# Email del service account (formato: <EMAIL>)
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Clave privada del service account (incluye -----BEGIN PRIVATE KEY----- y -----END PRIVATE KEY-----)
# IMPORTANTE: Mantén las comillas dobles y los \n para los saltos de línea
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nTU_CLAVE_PRIVADA_AQUI\n-----END PRIVATE KEY-----\n"

# -----------------------------------------------------------------------------
# CONFIGURACIÓN DE FIRESTORE (REQUERIDO)
# -----------------------------------------------------------------------------
# Configuración de la colección y documento principal en Firestore

# Nombre de la colección principal (ej: companies, clients, etc.)
COLLECTION=companies

# ID del documento principal dentro de la colección
DOC_ID=tu-documento-id

# -----------------------------------------------------------------------------
# CONFIGURACIÓN DEL SERVIDOR (OPCIONAL)
# -----------------------------------------------------------------------------
# Puerto donde se ejecutará el servidor (default: 3008)
PORT=3008

# -----------------------------------------------------------------------------
# INTEGRACIÓN CON ZOHO CALENDAR (OPCIONAL)
# -----------------------------------------------------------------------------
# Estas variables son necesarias solo si usas la integración con Zoho Calendar
# Ve a: Zoho API Console > Create Client > Server-based Applications

# Client ID de la aplicación Zoho
ZOHO_CLIENT_ID=1000.XXXXXXXXXXXXXXXXXXXXXXXXXX

# Client Secret de la aplicación Zoho  
ZOHO_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# URL de redirección configurada en Zoho (debe coincidir exactamente)
ZOHO_REDIRECT_URL=http://localhost:3000/oauth2callback

# UID del calendario de Zoho (se obtiene después de la autenticación)
ZOHO_CALENDAR_UID=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# =============================================================================
# INSTRUCCIONES DE CONFIGURACIÓN
# =============================================================================
#
# 1. FIREBASE SETUP:
#    - Ve a Firebase Console (https://console.firebase.google.com)
#    - Selecciona tu proyecto o crea uno nuevo
#    - Ve a Project Settings > Service Accounts
#    - Click en "Generate new private key"
#    - Descarga el archivo JSON y extrae los valores necesarios
#
# 2. FIRESTORE SETUP:
#    - Crea una base de datos Firestore en tu proyecto Firebase
#    - Crea una colección (ej: "companies")
#    - Crea un documento dentro de esa colección
#    - Usa el nombre de la colección y el ID del documento en las variables
#
# 3. ZOHO CALENDAR SETUP (Opcional):
#    - Ve a Zoho API Console (https://api-console.zoho.com)
#    - Crea una nueva aplicación "Server-based Applications"
#    - Configura la URL de redirección
#    - Obtén el Client ID y Client Secret
#    - Sigue el flujo OAuth para obtener el Calendar UID
#
# 4. VERIFICACIÓN:
#    - Ejecuta: npm run build
#    - Ejecuta: npm start
#    - Verifica que no hay errores de validación
#
# =============================================================================
